<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>收藏功能测试页面</h1>
    
    <div class="test-section">
        <h2>基础功能测试</h2>
        <button class="test-button" onclick="testAddFavorite()">添加收藏</button>
        <button class="test-button" onclick="testRemoveFavorite()">移除收藏</button>
        <button class="test-button" onclick="testToggleFavorite()">切换收藏</button>
        <button class="test-button" onclick="testGetFavorites()">获取收藏列表</button>
        <div id="basic-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>数据管理测试</h2>
        <button class="test-button" onclick="testExportFavorites()">导出收藏</button>
        <button class="test-button" onclick="testImportFavorites()">导入收藏</button>
        <button class="test-button" onclick="testClearFavorites()">清空收藏</button>
        <div id="data-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>统计信息测试</h2>
        <button class="test-button" onclick="testGetStats()">获取统计</button>
        <div id="stats-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>导航页面测试</h2>
        <a href="/" class="test-button" style="text-decoration: none; display: inline-block;">打开导航页面</a>
        <p>在导航页面中：</p>
        <ul>
            <li>检查侧边栏是否显示"我的收藏"分类</li>
            <li>检查网站卡片右上角是否显示收藏按钮（浅灰色"收藏"按钮）</li>
            <li>检查收藏按钮是否与文档按钮正确排列（收藏按钮在上，文档按钮在下）</li>
            <li>检查收藏按钮大小是否与文档按钮一致</li>
            <li>点击收藏按钮测试添加/移除收藏功能（浅灰色"收藏" ↔ 黄色"已收藏"）</li>
            <li>切换到"我的收藏"分类查看收藏的网站</li>
            <li>测试不同视图模式下的收藏按钮显示（标准、紧凑、大字体）</li>
            <li>测试不同主题下的收藏按钮样式</li>
        </ul>
    </div>

    <script>
        // 等待导航应用加载
        function waitForNavApp() {
            return new Promise((resolve) => {
                if (window.navApp && window.navApp.favoriteManager) {
                    resolve();
                } else {
                    setTimeout(() => waitForNavApp().then(resolve), 100);
                }
            });
        }

        function log(message, type = 'info') {
            console.log(message);
            return `<span class="${type}">${message}</span><br>`;
        }

        async function testAddFavorite() {
            const result = document.getElementById('basic-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const testSite = {
                    id: 'test-site-1',
                    name: '测试网站1',
                    url: 'https://example.com',
                    description: '这是一个测试网站'
                };
                
                const success = window.navApp.favoriteManager.addFavorite(
                    testSite.id, 
                    testSite.name, 
                    testSite
                );
                
                result.innerHTML = log(success ? '✓ 添加收藏成功' : '✗ 添加收藏失败', success ? 'success' : 'error');
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testRemoveFavorite() {
            const result = document.getElementById('basic-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const success = window.navApp.favoriteManager.removeFavorite('test-site-1');
                result.innerHTML = log(success ? '✓ 移除收藏成功' : '✗ 移除收藏失败', success ? 'success' : 'error');
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testToggleFavorite() {
            const result = document.getElementById('basic-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const testSite = {
                    id: 'test-site-2',
                    name: '测试网站2',
                    url: 'https://example2.com'
                };
                
                const isFavorited = window.navApp.favoriteManager.toggleFavorite(
                    testSite.id, 
                    testSite.name, 
                    testSite
                );
                
                result.innerHTML = log(`✓ 切换收藏成功，当前状态: ${isFavorited ? '已收藏' : '未收藏'}`, 'success');
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testGetFavorites() {
            const result = document.getElementById('basic-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const favorites = window.navApp.favoriteManager.getFavorites();
                result.innerHTML = log(`✓ 获取收藏列表成功，共 ${favorites.length} 个收藏`, 'success') +
                                 log(`收藏列表: ${JSON.stringify(favorites, null, 2)}`);
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testExportFavorites() {
            const result = document.getElementById('data-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const exportData = window.navApp.favoriteManager.exportFavorites();
                result.innerHTML = log('✓ 导出收藏成功', 'success') +
                                 log(`导出数据: ${exportData}`);
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testImportFavorites() {
            const result = document.getElementById('data-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const testData = JSON.stringify([
                    {
                        siteId: 'imported-site-1',
                        siteName: '导入的网站1',
                        addedAt: Date.now()
                    }
                ]);
                
                const success = window.navApp.favoriteManager.importFavorites(testData);
                result.innerHTML = log(success ? '✓ 导入收藏成功' : '✗ 导入收藏失败', success ? 'success' : 'error');
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testClearFavorites() {
            const result = document.getElementById('data-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                window.navApp.favoriteManager.clearFavorites();
                result.innerHTML = log('✓ 清空收藏成功', 'success');
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testGetStats() {
            const result = document.getElementById('stats-result');
            result.innerHTML = '';
            
            try {
                await waitForNavApp();
                const stats = window.navApp.favoriteManager.getStats();
                result.innerHTML = log('✓ 获取统计信息成功', 'success') +
                                 log(`统计信息: ${JSON.stringify(stats, null, 2)}`);
            } catch (error) {
                result.innerHTML = log(`✗ 测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示状态
        window.addEventListener('load', async () => {
            try {
                await waitForNavApp();
                console.log('✓ 导航应用已加载，收藏功能可用');
            } catch (error) {
                console.error('✗ 导航应用加载失败:', error);
            }
        });
    </script>
</body>
</html>
