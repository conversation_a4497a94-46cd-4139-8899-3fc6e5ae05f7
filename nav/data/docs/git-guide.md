# GitLab 使用指南

## 概述

GitLab 是我们的代码版本管理仓库，提供完整的 Git 版本控制功能和协作开发工具。

## 访问地址

- **主页**: https://git.firstshare.cn/
- **登录**: 使用公司账号登录

## 主要功能

### 1. 代码仓库管理
- **创建仓库**: 支持创建公开和私有仓库
- **分支管理**: 创建、合并、删除分支
- **标签管理**: 为重要版本打标签
- **权限控制**: 精细化的访问权限管理

### 2. 协作开发
- **合并请求 (Merge Request)**: 代码审查和合并流程
- **问题跟踪 (Issues)**: 缺陷跟踪和任务管理
- **里程碑 (Milestones)**: 项目进度管理
- **Wiki**: 项目文档管理

### 3. CI/CD 集成
- **GitLab CI**: 持续集成和部署
- **Pipeline**: 自动化构建流水线
- **Runner**: 构建执行器管理
- **环境管理**: 部署环境配置

## 最近访问操作

### 克隆仓库
```bash
git clone https://git.firstshare.cn/your-group/your-project.git
```

### 创建分支
```bash
git checkout -b feature/new-feature
git push -u origin feature/new-feature
```

### 提交代码
```bash
git add .
git commit -m "feat: 添加新功能"
git push origin feature/new-feature
```

### 创建合并请求
1. 在 GitLab 界面点击 "New merge request"
2. 选择源分支和目标分支
3. 填写标题和描述
4. 指定审查者
5. 提交合并请求

## 分支管理规范

### 分支命名规范
- **主分支**: `master` 或 `main`
- **开发分支**: `develop`
- **功能分支**: `feature/功能名称`
- **修复分支**: `hotfix/问题描述`
- **发布分支**: `release/版本号`

### 提交信息规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type)**:
- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 权限管理

### 角色权限
- **Owner**: 完全控制权限
- **Maintainer**: 项目维护权限
- **Developer**: 开发权限
- **Reporter**: 只读权限
- **Guest**: 访客权限

### 保护分支
- 主分支通常设置为保护分支
- 需要通过合并请求才能修改
- 可以设置必须的审查者数量

## 最佳实践

### 1. 代码审查
- 所有代码变更都应通过合并请求
- 至少需要一个同事审查
- 审查重点关注代码质量、安全性、性能

### 2. 分支策略
- 使用 Git Flow 或 GitHub Flow
- 保持主分支稳定
- 定期清理已合并的分支

### 3. 提交规范
- 提交信息要清晰明确
- 每次提交应该是一个逻辑单元
- 避免提交无关文件

### 4. 文档维护
- 及时更新 README.md
- 使用 Wiki 记录重要信息
- 为复杂功能编写使用文档

## 常见问题

### Q: 如何解决合并冲突？
A: 
1. 拉取最新代码: `git pull origin master`
2. 解决冲突文件中的标记
3. 提交解决结果: `git add . && git commit`
4. 推送到远程: `git push`

### Q: 如何回滚提交？
A:
```bash
# 回滚到指定提交
git reset --hard <commit-hash>

# 创建反向提交
git revert <commit-hash>
```

### Q: 如何查看提交历史？
A:
```bash
# 查看提交历史
git log --oneline --graph

# 查看文件变更历史
git log --follow <filename>
```

## 联系支持

如果在使用 GitLab 过程中遇到问题，可以：

1. 查看 GitLab 官方文档
2. 联系运维团队
3. 在内部技术群咨询

---

**更新时间**: 2024年12月
**维护团队**: 研发中心运维团队