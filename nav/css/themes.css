/* 日光象牙白主题 (原浅色主题优化版) */
[data-theme="ivory-light"] {
    --background-color: #fefefe;
    --surface-color: #f9f9f7;
    --surface-hover: #f3f3f1;
    --card-background: #ffffff;
    
    --text-primary: #2d2d2d;
    --text-secondary: #5a5a5a;
    --text-muted: #8a8a8a;
    
    --border-color: #e8e6e3;
    --border-hover: #d4d1cc;
    
    --shadow-sm: 0 1px 2px 0 rgba(45, 45, 45, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(45, 45, 45, 0.08), 0 2px 4px -1px rgba(45, 45, 45, 0.04);
    --shadow-lg: 0 10px 15px -3px rgba(45, 45, 45, 0.08), 0 4px 6px -2px rgba(45, 45, 45, 0.04);
    
    /* 主题色系 - 修复缺失的accent变量 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --accent-color: #8b5cf6;
    --accent-hover: #7c3aed;
    --accent-color-alpha: rgba(139, 92, 246, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* 搜索高亮色系 - 日光象牙白主题 */
    --highlight-bg: rgba(139, 92, 246, 0.15);
    --highlight-text: var(--text-primary);
    --highlight-border: rgba(139, 92, 246, 0.3);

    /* 管道搜索高亮色系 */
    --pipeline-highlight-1-bg: rgba(239, 68, 68, 0.12);
    --pipeline-highlight-1-text: #dc2626;
    --pipeline-highlight-1-border: rgba(239, 68, 68, 0.25);

    --pipeline-highlight-2-bg: rgba(34, 197, 94, 0.12);
    --pipeline-highlight-2-text: #16a34a;
    --pipeline-highlight-2-border: rgba(34, 197, 94, 0.25);

    --pipeline-highlight-3-bg: rgba(59, 130, 246, 0.12);
    --pipeline-highlight-3-text: #2563eb;
    --pipeline-highlight-3-border: rgba(59, 130, 246, 0.25);

    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
}

/* 夜月玄玉黑主题 (原深色主题优化版) */
[data-theme="dark-obsidian"] {
    /* 基础背景色系 */
    --background-color: #0f0f0f;
    --surface-color: #1a1a1a;
    --surface-hover: #2a2a2a;
    --card-background: #1e1e1e;
    
    /* 文字色系 - 提高对比度 */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* 边框色系 */
    --border-color: #374151;
    --border-hover: #4b5563;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.5);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
    
    /* 主题色系 - 补充缺失的变量 */
    --primary-color: #60a5fa;
    --primary-hover: #3b82f6;
    --accent-color: #a78bfa;
    --accent-hover: #8b5cf6;
    --accent-color-alpha: rgba(167, 139, 250, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* 搜索高亮色系 - 夜月玄玉黑主题 */
    --highlight-bg: rgba(167, 139, 250, 0.25);
    --highlight-text: #e2e8f0;
    --highlight-border: rgba(167, 139, 250, 0.4);

    /* 管道搜索高亮色系 - 深色主题优化 */
    --pipeline-highlight-1-bg: rgba(248, 113, 113, 0.2);
    --pipeline-highlight-1-text: #fca5a5;
    --pipeline-highlight-1-border: rgba(248, 113, 113, 0.35);

    --pipeline-highlight-2-bg: rgba(74, 222, 128, 0.2);
    --pipeline-highlight-2-text: #86efac;
    --pipeline-highlight-2-border: rgba(74, 222, 128, 0.35);

    --pipeline-highlight-3-bg: rgba(96, 165, 250, 0.2);
    --pipeline-highlight-3-text: #93c5fd;
    --pipeline-highlight-3-border: rgba(96, 165, 250, 0.35);

    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
}

/* 清雅茉莉绿主题 - 专业重构版 */
[data-theme="jasmine-green"] {
    /* 基础背景色系 - 茉莉花瓣的渐变白绿 */
    --background-color: #f8fdfa;
    --surface-color: rgba(240, 253, 244, 0.7);
    --surface-hover: rgba(233, 250, 240, 0.9);
    --card-background: rgba(255, 255, 255, 0.95);
    
    /* 文字色系 - 深度绿色调，确保对比度 */
    --text-primary: #1a3d2e;
    --text-secondary: #2d5a47;
    --text-muted: #4a8066;
    
    /* 边框色系 - 柔和的茉莉绿边框 */
    --border-color: rgba(187, 233, 207, 0.4);
    --border-hover: rgba(167, 225, 192, 0.6);
    
    /* 阴影系统 - 带绿色调的柔和阴影 */
    --shadow-sm: 0 1px 3px 0 rgba(34, 84, 61, 0.08), 0 1px 2px 0 rgba(34, 84, 61, 0.04);
    --shadow-md: 0 4px 12px -2px rgba(34, 84, 61, 0.12), 0 2px 8px -1px rgba(34, 84, 61, 0.08);
    --shadow-lg: 0 12px 24px -4px rgba(34, 84, 61, 0.15), 0 4px 16px -2px rgba(34, 84, 61, 0.1);
    
    /* 主题色系 - 修复缺失的变量 */
    --primary-color: #10b981;
    --primary-hover: #059669;
    --accent-color: #22c55e;
    --accent-hover: #16a34a;
    --accent-color-alpha: rgba(34, 197, 94, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #22c55e;

    /* 搜索高亮色系 - 清雅茉莉绿主题 */
    --highlight-bg: rgba(34, 197, 94, 0.15);
    --highlight-text: var(--text-primary);
    --highlight-border: rgba(34, 197, 94, 0.3);

    /* 管道搜索高亮色系 - 茉莉绿主题优化 */
    --pipeline-highlight-1-bg: rgba(239, 68, 68, 0.1);
    --pipeline-highlight-1-text: #dc2626;
    --pipeline-highlight-1-border: rgba(239, 68, 68, 0.2);

    --pipeline-highlight-2-bg: rgba(16, 185, 129, 0.15);
    --pipeline-highlight-2-text: #059669;
    --pipeline-highlight-2-border: rgba(16, 185, 129, 0.3);

    --pipeline-highlight-3-bg: rgba(59, 130, 246, 0.1);
    --pipeline-highlight-3-text: #2563eb;
    --pipeline-highlight-3-border: rgba(59, 130, 246, 0.2);
    
    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    
    /* 茉莉绿主题专属变量 */
    --jasmine-primary: #22c55e;
    --jasmine-secondary: #16a34a;
    --jasmine-accent: #dcfce7;
    --jasmine-soft: #f0fdf4;
    --jasmine-white: #fefffe;
    --jasmine-mist: rgba(240, 253, 244, 0.6);
}

/* 背景渐变效果 */
[data-theme="jasmine-green"] body {
    background: linear-gradient(135deg, #f8fdfa 0%, #f0faf4 25%, #f4fcf6 50%, #f0faf4 75%, #f8fdfa 100%);
    background-attachment: fixed;
}

/* 导航栏茉莉绿主题 */
[data-theme="jasmine-green"] .navbar-custom {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(187, 233, 207, 0.3);
    box-shadow: 0 1px 12px rgba(34, 84, 61, 0.08);
}

/* 品牌文字渐变 - 茉莉花的渐变美感 */
[data-theme="jasmine-green"] .brand-text {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 25%, #4ade80 50%, #22c55e 75%, #16a34a 100%);
    background-size: 200% 200%;
    animation: jasmine-gradient 4s ease-in-out infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes jasmine-gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 搜索框茉莉绿主题 */
[data-theme="jasmine-green"] .search-input {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(187, 233, 207, 0.4);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="jasmine-green"] .search-input:focus {
    background: rgba(255, 255, 255, 0.95);
    border-color: var(--jasmine-primary);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1), 
                0 4px 12px rgba(34, 84, 61, 0.15);
}

[data-theme="jasmine-green"] .search-shortcut {
    background: var(--jasmine-accent);
    color: var(--jasmine-secondary);
    border: 1px solid rgba(187, 233, 207, 0.3);
}

/* 侧边栏茉莉绿主题 */
[data-theme="jasmine-green"] .sidebar {
    background: rgba(248, 253, 250, 0.9);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(187, 233, 207, 0.3);
}

[data-theme="jasmine-green"] .category-name:hover {
    color: var(--jasmine-secondary);
    background: var(--jasmine-accent);
    transform: translateX(4px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="jasmine-green"] .category-name.active {
    background: linear-gradient(135deg, var(--jasmine-primary), var(--jasmine-secondary));
    color: white;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

/* 卡片系统茉莉绿主题 */
[data-theme="jasmine-green"] .site-card {
    background: var(--jasmine-white);
    border: 1px solid rgba(187, 233, 207, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="jasmine-green"] .site-card:hover {
    background: var(--jasmine-white);
    border-color: rgba(34, 197, 94, 0.3);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 32px rgba(34, 84, 61, 0.12), 
                0 4px 16px rgba(34, 197, 94, 0.08),
                0 0 0 1px rgba(34, 197, 94, 0.05);
}

/* 网站图标茉莉绿主题 */
[data-theme="jasmine-green"] .site-icon {
    background: var(--jasmine-soft);
    border: 1px solid rgba(187, 233, 207, 0.3);
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .site-card:hover .site-icon {
    background: var(--jasmine-accent);
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

/* 标签系统茉莉绿主题 */
[data-theme="jasmine-green"] .site-tag {
    background: var(--jasmine-accent);
    color: var(--jasmine-secondary);
    border: 1px solid rgba(34, 197, 94, 0.2);
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .site-tag:hover {
    background: var(--jasmine-primary);
    color: white;
    transform: scale(1.05);
}

/* 按钮系统茉莉绿主题 */
[data-theme="jasmine-green"] .action-btn:hover {
    background: var(--jasmine-accent);
    color: var(--jasmine-secondary);
    transform: scale(1.05);
}

[data-theme="jasmine-green"] .theme-selector-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(187, 233, 207, 0.4);
    backdrop-filter: blur(10px);
}

[data-theme="jasmine-green"] .theme-selector-btn:hover {
    background: var(--jasmine-white);
    border-color: var(--jasmine-primary);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
}

/* 下拉菜单茉莉绿主题 */
/* 移除旧的主题特定样式，现在使用统一的固定配色方案 */

/* 搜索结果茉莉绿主题 */
[data-theme="jasmine-green"] .search-result-item:hover {
    background: var(--jasmine-accent);
    border-radius: 8px;
    transform: translateX(4px);
}

[data-theme="jasmine-green"] .search-result-tag {
    background: var(--jasmine-soft);
    color: var(--jasmine-secondary);
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* 滚动条茉莉绿主题 */
[data-theme="jasmine-green"] ::-webkit-scrollbar-track {
    background: var(--jasmine-soft);
}

[data-theme="jasmine-green"] ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--jasmine-primary), var(--jasmine-secondary));
    border-radius: 6px;
    border: 2px solid var(--jasmine-soft);
}

[data-theme="jasmine-green"] ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--jasmine-secondary), #15803d);
}

/* 加载状态茉莉绿主题 */
[data-theme="jasmine-green"] .loading-spinner .spinner-border {
    color: var(--jasmine-primary);
}

/* 空状态茉莉绿主题 */
[data-theme="jasmine-green"] .empty-state {
    color: var(--jasmine-secondary);
}

[data-theme="jasmine-green"] .empty-state i {
    color: var(--jasmine-primary);
    opacity: 0.6;
}

/* Markdown模态框茉莉绿主题 */
[data-theme="jasmine-green"] .markdown-modal-backdrop {
    background: rgba(34, 84, 61, 0.4);
    backdrop-filter: blur(8px);
}

[data-theme="jasmine-green"] .markdown-modal-content {
    background: var(--jasmine-white);
    border: 1px solid rgba(187, 233, 207, 0.3);
    box-shadow: 0 24px 64px rgba(34, 84, 61, 0.15), 
                0 12px 32px rgba(34, 84, 61, 0.1);
}

[data-theme="jasmine-green"] .markdown-modal-header {
    border-bottom: 1px solid rgba(187, 233, 207, 0.3);
    background: var(--jasmine-soft);
}

[data-theme="jasmine-green"] .markdown-modal-close:hover {
    background: var(--jasmine-accent);
    color: var(--jasmine-secondary);
}

[data-theme="jasmine-green"] .markdown-modal-action-btn:hover {
    background: var(--jasmine-accent);
    color: var(--jasmine-secondary);
}

/* 特殊效果 - 茉莉花瓣飘落动画 */
[data-theme="jasmine-green"] .site-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        transparent, 
        rgba(34, 197, 94, 0.1), 
        transparent, 
        rgba(34, 197, 94, 0.1), 
        transparent);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

[data-theme="jasmine-green"] .site-card:hover::before {
    opacity: 1;
}

/* 响应式优化 */
@media (max-width: 768px) {
    [data-theme="jasmine-green"] .navbar-custom {
        backdrop-filter: blur(15px);
    }
    
    [data-theme="jasmine-green"] .site-card:hover {
        transform: translateY(-2px) scale(1.01);
    }
}

/* 高对比度优化（保证可访问性） */
@media (prefers-contrast: high) {
    [data-theme="jasmine-green"] {
        --text-primary: #0d2818;
        --text-secondary: #1a3d2e;
        --border-color: rgba(34, 197, 94, 0.6);
    }
}

/* 减少动画（尊重用户偏好） */
@media (prefers-reduced-motion: reduce) {
    [data-theme="jasmine-green"] * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 深邃海军蓝主题 */
[data-theme="navy-blue"] {
    --background-color: #1e2838;
    --surface-color: #243447;
    --surface-hover: #2a3d56;
    --card-background: #2a3d56;
    
    --text-primary: #e8f1ff;
    --text-secondary: #b8d4f0;
    --text-muted: #8ab0d4;
    
    --border-color: #3a4d66;
    --border-hover: #4a5d76;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.25);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.25), 0 2px 4px -1px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
    
    /* 主题色系 - 修复缺失的accent变量 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --accent-color: #7dd3fc;
    --accent-hover: #38bdf8;
    --accent-color-alpha: rgba(125, 211, 252, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* 搜索高亮色系 - 深邃海军蓝主题 */
    --highlight-bg: rgba(125, 211, 252, 0.2);
    --highlight-text: #e8f1ff;
    --highlight-border: rgba(125, 211, 252, 0.35);

    /* 管道搜索高亮色系 - 海军蓝主题优化 */
    --pipeline-highlight-1-bg: rgba(248, 113, 113, 0.15);
    --pipeline-highlight-1-text: #fca5a5;
    --pipeline-highlight-1-border: rgba(248, 113, 113, 0.3);

    --pipeline-highlight-2-bg: rgba(74, 222, 128, 0.15);
    --pipeline-highlight-2-text: #86efac;
    --pipeline-highlight-2-border: rgba(74, 222, 128, 0.3);

    --pipeline-highlight-3-bg: rgba(125, 211, 252, 0.2);
    --pipeline-highlight-3-text: #7dd3fc;
    --pipeline-highlight-3-border: rgba(125, 211, 252, 0.35);

    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
}

/* 兼容性：保留原有的data-theme="light"和"dark"支持 */
[data-theme="light"] {
    --background-color: #fefefe;
    --surface-color: #f9f9f7;
    --surface-hover: #f3f3f1;
    --card-background: #ffffff;
    
    --text-primary: #2d2d2d;
    --text-secondary: #5a5a5a;
    --text-muted: #8a8a8a;
    
    --border-color: #e8e6e3;
    --border-hover: #d4d1cc;
    
    --shadow-sm: 0 1px 2px 0 rgba(45, 45, 45, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(45, 45, 45, 0.08), 0 2px 4px -1px rgba(45, 45, 45, 0.04);
    --shadow-lg: 0 10px 15px -3px rgba(45, 45, 45, 0.08), 0 4px 6px -2px rgba(45, 45, 45, 0.04);
    
    /* 主题色系 - 兼容性支持 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --accent-color: #8b5cf6;
    --accent-hover: #7c3aed;
    --accent-color-alpha: rgba(139, 92, 246, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
}

[data-theme="dark"] {
    --background-color: #0f0f0f;
    --surface-color: #1a1a1a;
    --surface-hover: #2a2a2a;
    --card-background: #1a1a1a;
    
    --text-primary: #f5f5f5;
    --text-secondary: #c0c0c0;
    --text-muted: #909090;
    
    --border-color: #2a2a2a;
    --border-hover: #3a3a3a;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    
    /* 主题色系 - 兼容性支持 */
    --primary-color: #60a5fa;
    --primary-hover: #3b82f6;
    --accent-color: #a78bfa;
    --accent-hover: #8b5cf6;
    --accent-color-alpha: rgba(167, 139, 250, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
}

/* 深色系主题通用样式 (dark-obsidian, navy-blue) */
[data-theme="dark-obsidian"] .search-input,
[data-theme="navy-blue"] .search-input {
    background-color: var(--surface-color);
}

[data-theme="dark-obsidian"] .search-input:focus,
[data-theme="navy-blue"] .search-input:focus {
    background-color: var(--card-background);
}

[data-theme="dark-obsidian"] .search-shortcut,
[data-theme="navy-blue"] .search-shortcut {
    background-color: var(--surface-hover);
    color: var(--text-muted);
}

[data-theme="dark-obsidian"] .subcategory-list,
[data-theme="navy-blue"] .subcategory-list {
    background-color: rgba(255, 255, 255, 0.02);
}

[data-theme="dark-obsidian"] .site-icon,
[data-theme="navy-blue"] .site-icon {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .site-tag,
[data-theme="navy-blue"] .site-tag {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .category-arrow:hover,
[data-theme="navy-blue"] .category-arrow:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .category-name:hover {
    color: #60a5fa;
}

[data-theme="navy-blue"] .category-name:hover {
    color: #7dd3fc;
}

/* 浅色系主题通用样式 (ivory-light, jasmine-green) */
[data-theme="ivory-light"] .search-input,
[data-theme="jasmine-green"] .search-input {
    background-color: var(--surface-color);
}

[data-theme="ivory-light"] .search-input:focus,
[data-theme="jasmine-green"] .search-input:focus {
    background-color: var(--card-background);
}

[data-theme="jasmine-green"] .category-name:hover {
    color: #059669;
}

/* 清雅茉莉绿主题特殊样式 */
[data-theme="jasmine-green"] .brand-text {
    background: linear-gradient(135deg, #059669, #10b981);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="jasmine-green"] .site-card:hover {
    box-shadow: 0 10px 25px -3px rgba(5, 150, 105, 0.1), 0 4px 6px -2px rgba(5, 150, 105, 0.05);
}

/* 深邃海军蓝主题特殊样式 */
[data-theme="navy-blue"] .brand-text {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

[data-theme="navy-blue"] .site-card:hover {
    box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.15), 0 4px 6px -2px rgba(59, 130, 246, 0.1);
}

/* 夜月玄玉黑主题特殊样式 */
[data-theme="dark-obsidian"] .brand-text {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 网站卡片夜月玄玉黑主题优化 */
[data-theme="dark-obsidian"] .site-card {
    background-color: #1e1e1e;
    border: 1px solid #374151;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark-obsidian"] .site-card:hover {
    background-color: #262626;
    border-color: #4b5563;
    box-shadow: 0 12px 28px -4px rgba(0, 0, 0, 0.4), 
                0 6px 16px -2px rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(96, 165, 250, 0.1);
    transform: translateY(-4px);
}

[data-theme="dark-obsidian"] .site-title {
    color: #f8fafc;
}

[data-theme="dark-obsidian"] .site-url {
    color: #94a3b8;
}

[data-theme="dark-obsidian"] .site-description {
    color: #cbd5e1;
}

/* 保留原深色主题样式以保持兼容性 */
[data-theme="dark"] .search-input {
    background-color: var(--surface-color);
}

[data-theme="dark"] .search-input:focus {
    background-color: var(--card-background);
}

[data-theme="dark"] .search-shortcut {
    background-color: var(--surface-hover);
    color: var(--text-muted);
}

[data-theme="dark"] .subcategory-list {
    background-color: rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .site-icon {
    background-color: var(--surface-hover);
}

[data-theme="dark"] .site-tag {
    background-color: var(--surface-hover);
}

/* 深色主题下的收藏按钮 */
[data-theme="dark"] .favorite-btn {
    background-color: rgb(33 109 30 / 80%); /* 深色主题下也使用相同的柔和绿色 */
    color: white;
}

[data-theme="dark"] .favorite-btn:hover {
    background-color: rgb(33 109 30 / 100%);
}

[data-theme="dark"] .favorite-btn.favorited {
    background-color: var(--warning-color); /* 与文档按钮相同的黄色 */
}

[data-theme="dark"] .favorite-btn.favorited:hover {
    background-color: var(--warning-hover, #e09900);
}

[data-theme="dark"] .category-arrow:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark"] .category-name:hover {
    color: #60a5fa;
}

/* 主题切换动画 */
* {
    transition-property: background-color, border-color, color, box-shadow;
    transition-duration: var(--transition-normal);
    transition-timing-function: ease;
}

/* 主题选择器样式 - 独立配色方案，确保在所有主题下都有良好对比度 */
.theme-selector-dropdown {
    position: relative;
    display: inline-block;
}

.theme-selector-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 14px;
    white-space: nowrap;
}

.theme-selector-btn:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

.theme-dropdown-arrow {
    font-size: 10px;
    transition: transform var(--transition-fast);
}

.theme-selector-btn[aria-expanded="true"] .theme-dropdown-arrow,
.theme-dropdown-menu.show + .theme-selector-btn .theme-dropdown-arrow {
    transform: rotate(180deg);
}

/* 主题下拉菜单 - 使用固定配色确保对比度 */
.theme-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 300px;
    margin-top: 4px;
    /* 使用固定的配色方案，不依赖主题变量 */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 12px;
    /* 增强阴影效果 */
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1), 
                0 8px 30px rgba(0, 0, 0, 0.05),
                0 2px 8px rgba(0, 0, 0, 0.03);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1050;
    max-height: 400px;
    overflow-y: auto;
    /* 增加背景模糊效果 */
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
}

.theme-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 主题选项卡片 - 固定配色方案，确保在所有主题下都有良好对比度 */
.theme-option {
    display: flex;
    align-items: center;
    gap: 14px;
    padding: 14px 18px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 10px;
    margin: 6px 8px;
    position: relative;
    /* 固定深色文字，适配浅色背景 */
    color: #0f172a !important;
    background: transparent;
    /* WCAG AAA 级别对比度保证 */
}

.theme-option:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    color: #020617 !important;
}

.theme-option:hover .theme-option-name {
    color: #020617 !important;
}

.theme-option:hover .theme-option-desc {
    color: #334155 !important;
}

.theme-option:hover .theme-option-icon {
    color: #334155 !important;
}

.theme-option.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
}

.theme-option.active::before {
    content: "✓";
    position: absolute;
    top: 8px;
    right: 12px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.theme-option-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
    /* 固定深灰色图标，确保在浅色菜单背景下清晰可见 */
    color: #475569 !important;
    transition: all 0.2s ease;
}

.theme-option.active .theme-option-icon {
    color: white;
    transform: scale(1.1);
}

.theme-option-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.theme-option-name {
    font-weight: 600;
    font-size: 15px;
    /* 固定深色文字，确保在浅色菜单背景下清晰可见 */
    color: #020617 !important;
    transition: color 0.2s ease;
}

.theme-option.active .theme-option-name {
    color: white;
}

.theme-option-desc {
    font-size: 13px;
    line-height: 1.4;
    /* 固定深灰色文字，确保在浅色菜单背景下清晰可见 */
    color: #475569 !important;
    transition: color 0.2s ease;
}

.theme-option.active .theme-option-desc {
    color: rgba(255, 255, 255, 0.9);
}

/* 简化的主题选择器对比度修复 - 最高优先级 */
html[data-theme="ivory-light"] .theme-option-name,
html[data-theme="jasmine-green"] .theme-option-name,
html[data-theme="navy-blue"] .theme-option-name,
body[data-theme="ivory-light"] .theme-option-name,
body[data-theme="jasmine-green"] .theme-option-name,
body[data-theme="navy-blue"] .theme-option-name {
    color: #000000 !important;
}

html[data-theme="ivory-light"] .theme-option-desc,
html[data-theme="jasmine-green"] .theme-option-desc,
html[data-theme="navy-blue"] .theme-option-desc,
body[data-theme="ivory-light"] .theme-option-desc,
body[data-theme="jasmine-green"] .theme-option-desc,
body[data-theme="navy-blue"] .theme-option-desc {
    color: #333333 !important;
}

/* 深色模式下的特殊处理 */
[data-theme="dark-obsidian"] .theme-dropdown-menu {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border: 1px solid rgba(71, 85, 105, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4), 
                0 8px 30px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 深色主题使用浅色文字 - 最高优先级 */
html[data-theme="dark-obsidian"] .theme-option:hover,
body[data-theme="dark-obsidian"] .theme-option:hover {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

html[data-theme="dark-obsidian"] .theme-option-name,
body[data-theme="dark-obsidian"] .theme-option-name {
    color: #ffffff !important;
}

html[data-theme="dark-obsidian"] .theme-option-desc,
body[data-theme="dark-obsidian"] .theme-option-desc {
    color: #cccccc !important;
}

/* 其他主题的特殊处理 */
[data-theme="jasmine-green"] .theme-dropdown-menu {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    box-shadow: 0 20px 60px rgba(34, 197, 94, 0.08), 
                0 8px 30px rgba(34, 197, 94, 0.04),
                0 2px 8px rgba(34, 197, 94, 0.02);
}

[data-theme="jasmine-green"] .theme-option:hover {
    background: linear-gradient(135deg, #bbf7d0 0%, #a7f3d0 100%);
}

[data-theme="navy-blue"] .theme-dropdown-menu {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 20px 60px rgba(59, 130, 246, 0.08), 
                0 8px 30px rgba(59, 130, 246, 0.04),
                0 2px 8px rgba(59, 130, 246, 0.02);
}

[data-theme="navy-blue"] .theme-option:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
}

/* 滚动条样式 */
.theme-dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.theme-dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
}

.theme-dropdown-menu::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1, #94a3b8);
    border-radius: 3px;
}

.theme-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #94a3b8, #64748b);
}

[data-theme="dark-obsidian"] .theme-dropdown-menu::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #475569, #334155);
}

[data-theme="dark-obsidian"] .theme-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #64748b, #475569);
}

/* 主题切换按钮动画（保持兼容性） */
.theme-btn i {
    transition: transform var(--transition-normal);
}

.theme-btn:hover i {
    transform: rotate(180deg);
}

/* 渐变背景在日光象牙白主题下的调整 */
[data-theme="ivory-light"] .brand-text {
    background: linear-gradient(135deg, var(--primary-color), #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 卡片悬停效果在各主题下的调整 */
[data-theme="ivory-light"] .site-card:hover {
    box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

/* 搜索结果在所有主题下的样式 */
[data-theme="dark-obsidian"] .search-result-item:hover,
[data-theme="navy-blue"] .search-result-item:hover {
    background-color: var(--surface-hover);
}

[data-theme="ivory-light"] .search-result-item:hover,
[data-theme="jasmine-green"] .search-result-item:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .search-result-tag,
[data-theme="navy-blue"] .search-result-tag {
    background-color: var(--accent-color-alpha);
    color: var(--accent-color);
}

[data-theme="ivory-light"] .search-result-tag,
[data-theme="jasmine-green"] .search-result-tag {
    background-color: var(--accent-color-alpha);
    color: var(--accent-color);
}

/* 响应式设计 */
@media (max-width: 767px) {
    .theme-dropdown-menu {
        right: -16px;
        left: -16px;
        min-width: auto;
    }
    
    .theme-selector-btn .theme-name {
        display: none;
    }
}

/* 筛选器样式 - 针对不同主题的适配 */

/* 自定义下拉选择器主题适配 */

/* 日光象牙白主题 */
[data-theme="ivory-light"] .custom-select-trigger {
    background-color: var(--surface-color);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="ivory-light"] .custom-select-trigger:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

[data-theme="ivory-light"] .custom-select.open .custom-select-trigger {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background-color: var(--card-background);
}

[data-theme="ivory-light"] .custom-select-options {
    background-color: var(--card-background);
    border-color: var(--border-color);
    box-shadow: var(--shadow-lg);
}

[data-theme="ivory-light"] .custom-select-option {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="ivory-light"] .custom-select-option:hover {
    background-color: var(--surface-hover);
}

[data-theme="ivory-light"] .custom-select-option.active {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="ivory-light"] .custom-select-option.highlighted {
    background-color: var(--surface-hover);
}

/* 原生select兼容样式 */
[data-theme="ivory-light"] .filter-select {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="ivory-light"] .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background-color: var(--card-background);
}

[data-theme="ivory-light"] .filter-select option {
    background-color: var(--card-background);
    color: var(--text-primary);
}

/* 夜月玄玉黑主题 */
[data-theme="dark-obsidian"] .custom-select-trigger {
    background-color: var(--surface-color);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark-obsidian"] .custom-select-trigger:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

[data-theme="dark-obsidian"] .custom-select.open .custom-select-trigger {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
    background-color: var(--card-background);
}

[data-theme="dark-obsidian"] .custom-select-options {
    background-color: var(--card-background);
    border-color: var(--border-color);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3),
                0 8px 30px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="dark-obsidian"] .custom-select-option {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark-obsidian"] .custom-select-option:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .custom-select-option.active {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="dark-obsidian"] .custom-select-option.highlighted {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .custom-select-arrow {
    color: var(--text-muted);
}

/* 原生select兼容样式 */
[data-theme="dark-obsidian"] .filter-select {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    /* 确保下拉箭头在深色主题下可见 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cbd5e1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

[data-theme="dark-obsidian"] .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
    background-color: var(--card-background);
}

[data-theme="dark-obsidian"] .filter-select option {
    background-color: var(--card-background);
    color: var(--text-primary);
}

/* 清雅茉莉绿主题 - 美化版 */
[data-theme="jasmine-green"] {
    /* 主色调 - 茉莉花的优雅绿调 */
    --primary-color: #4a7c59;
    --primary-hover: #3d6b4a;
    --primary-light: #e8f5e8;
    --primary-dark: #2d4a35;
    --primary-gradient: linear-gradient(135deg, #4a7c59 0%, #5a8c69 100%);
    
    /* 背景色 - 茉莉花瓣般的纯净 */
    --bg-primary: #f9fdf9;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f2f9f2;
    --bg-hover: #eaf6ea;
    --bg-card: #ffffff;
    --bg-card-hover: #f8fcf8;
    
    /* 文字颜色 - 自然清新 */
    --text-primary: #2d4a35;
    --text-secondary: #4a7c59;
    --text-muted: #6b8e6b;
    --text-light: #8fa68f;
    --text-accent: #5a8c69;
    
    /* 边框颜色 - 柔和过渡 */
    --border-color: #d4e6d4;
    --border-hover: #b8d4b8;
    --border-focus: #4a7c59;
    --border-light: #e8f0e8;
    
    /* 阴影 - 茉莉花香般轻柔 */
    --shadow-light: 0 2px 12px rgba(74, 124, 89, 0.06);
    --shadow-medium: 0 4px 20px rgba(74, 124, 89, 0.1);
    --shadow-heavy: 0 8px 40px rgba(74, 124, 89, 0.15);
    --shadow-card: 0 3px 15px rgba(74, 124, 89, 0.08);
    --shadow-card-hover: 0 6px 25px rgba(74, 124, 89, 0.12);
    
    /* 特殊效果 */
    --accent-color: #7ba05b;
    --accent-gradient: linear-gradient(135deg, #7ba05b 0%, #8bb06b 100%);
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;
    
    /* 茉莉绿特有的装饰色 */
    --jasmine-light: #f0f8f0;
    --jasmine-medium: #e0f0e0;
    --jasmine-accent: #c8e6c8;
    --jasmine-deep: #a8d6a8;
}

/* 茉莉绿主题 - 导航栏美化 */
[data-theme="jasmine-green"] .navbar-custom {
    background: linear-gradient(135deg, #ffffff 0%, #f9fdf9 100%);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-light);
    backdrop-filter: blur(10px);
}

[data-theme="jasmine-green"] .navbar-brand-custom .logo {
    width: 32px;
    height: 32px;
    filter: drop-shadow(0 2px 4px rgba(74, 124, 89, 0.2));
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .navbar-brand-custom .logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 3px 6px rgba(74, 124, 89, 0.3));
}

[data-theme="jasmine-green"] .brand-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

/* 搜索框美化 */
[data-theme="jasmine-green"] .search-wrapper {
    background: var(--bg-card);
    border: 2px solid var(--border-light);
    border-radius: 16px;
    box-shadow: var(--shadow-card);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="jasmine-green"] .search-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-card-hover), 0 0 0 4px rgba(74, 124, 89, 0.1);
    transform: translateY(-1px);
}

[data-theme="jasmine-green"] .search-input {
    background: transparent;
    color: var(--text-primary);
}

[data-theme="jasmine-green"] .search-input::placeholder {
    color: var(--text-muted);
}

/* 主题选择器美化 */
[data-theme="jasmine-green"] .theme-selector-btn {
    background: var(--jasmine-light);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .theme-selector-btn:hover {
    background: var(--jasmine-medium);
    border-color: var(--border-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
}

[data-theme="jasmine-green"] .theme-dropdown-menu {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(20px);
}

[data-theme="jasmine-green"] .theme-option {
    border-radius: 12px;
    margin: 4px;
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .theme-option:hover {
    background: var(--jasmine-light);
    transform: translateX(4px);
}

[data-theme="jasmine-green"] .theme-option.active {
    background: var(--jasmine-medium);
    border: 1px solid var(--primary-color);
}

/* 侧边栏美化 */
[data-theme="jasmine-green"] .sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f9fdf9 100%);
    border-right: 1px solid var(--border-light);
    box-shadow: var(--shadow-light);
}

[data-theme="jasmine-green"] .category-item {
    border-radius: 12px;
    margin: 4px 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

[data-theme="jasmine-green"] .category-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

[data-theme="jasmine-green"] .category-item:hover::before,
[data-theme="jasmine-green"] .category-item.active::before {
    transform: scaleY(1);
}

[data-theme="jasmine-green"] .category-item:hover {
    background: var(--jasmine-light);
    transform: translateX(4px);
    box-shadow: var(--shadow-card);
}

[data-theme="jasmine-green"] .category-item.active {
    background: var(--jasmine-medium);
    color: var(--primary-dark);
    font-weight: 600;
}

/* 网站卡片美化 */
[data-theme="jasmine-green"] .site-card {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    box-shadow: var(--shadow-card);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

[data-theme="jasmine-green"] .site-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

[data-theme="jasmine-green"] .site-card:hover::before {
    transform: scaleX(1);
}

[data-theme="jasmine-green"] .site-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-card-hover);
    border-color: var(--border-hover);
    background: var(--bg-card-hover);
}

[data-theme="jasmine-green"] .site-icon {
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .site-card:hover .site-icon {
    transform: scale(1.05);
    box-shadow: var(--shadow-medium);
}

[data-theme="jasmine-green"] .site-title {
    color: var(--text-primary);
    font-weight: 600;
    transition: color 0.3s ease;
}

[data-theme="jasmine-green"] .site-card:hover .site-title {
    color: var(--primary-color);
}

[data-theme="jasmine-green"] .site-description {
    color: var(--text-muted);
    line-height: 1.5;
}

[data-theme="jasmine-green"] .site-tags .tag {
    background: var(--jasmine-light);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 2px 8px;
    font-size: 0.75rem;
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .site-tags .tag:hover {
    background: var(--jasmine-medium);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* 按钮美化 */
[data-theme="jasmine-green"] .action-btn {
    background: var(--jasmine-light);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    color: var(--text-secondary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="jasmine-green"] .action-btn:hover {
    background: var(--jasmine-medium);
    border-color: var(--border-hover);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
}

/* 搜索结果美化 */
[data-theme="jasmine-green"] .search-results {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(20px);
}

[data-theme="jasmine-green"] .search-result-item {
    border-radius: 12px;
    margin: 4px;
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .search-result-item:hover {
    background: var(--jasmine-light);
    transform: translateX(4px);
}

/* 加载动画美化 */
[data-theme="jasmine-green"] .loading-spinner .spinner-border {
    color: var(--primary-color);
    filter: drop-shadow(0 2px 4px rgba(74, 124, 89, 0.3));
}

/* 空状态美化 */
[data-theme="jasmine-green"] .empty-state {
    color: var(--text-muted);
}

[data-theme="jasmine-green"] .empty-state i {
    color: var(--jasmine-deep);
    filter: drop-shadow(0 2px 4px rgba(74, 124, 89, 0.2));
}

/* 模态框美化 */
[data-theme="jasmine-green"] .markdown-modal-content {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: 20px;
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(20px);
}

[data-theme="jasmine-green"] .markdown-modal-header {
    border-bottom: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--jasmine-light) 0%, var(--jasmine-medium) 100%);
}

[data-theme="jasmine-green"] .markdown-modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

/* 滚动条美化 */
[data-theme="jasmine-green"] ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

[data-theme="jasmine-green"] ::-webkit-scrollbar-track {
    background: var(--jasmine-light);
    border-radius: 4px;
}

[data-theme="jasmine-green"] ::-webkit-scrollbar-thumb {
    background: var(--jasmine-deep);
    border-radius: 4px;
    transition: background 0.3s ease;
}

[data-theme="jasmine-green"] ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* 特殊动画效果 */
[data-theme="jasmine-green"] .site-card,
[data-theme="jasmine-green"] .category-item,
[data-theme="jasmine-green"] .action-btn {
    animation: jasmine-fade-in 0.6s ease-out;
}

@keyframes jasmine-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 茉莉花瓣飘落效果 */
[data-theme="jasmine-green"] .navbar-custom::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(74, 124, 89, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(123, 160, 91, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* 茉莉绿主题 - 特殊装饰效果 */
[data-theme="jasmine-green"] .content-area::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 10% 20%, rgba(74, 124, 89, 0.02) 0%, transparent 40%),
        radial-gradient(circle at 90% 80%, rgba(123, 160, 91, 0.02) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(168, 214, 168, 0.01) 0%, transparent 60%);
    pointer-events: none;
    z-index: -1;
}

/* 茉莉花瓣装饰 */
[data-theme="jasmine-green"] .site-card::after {
    content: '🌸';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 12px;
    opacity: 0;
    transition: all 0.3s ease;
    transform: rotate(0deg);
}

[data-theme="jasmine-green"] .site-card:hover::after {
    opacity: 0.3;
    transform: rotate(15deg) scale(1.2);
}

/* 分类图标增强 */
[data-theme="jasmine-green"] .category-item .category-icon {
    filter: drop-shadow(0 1px 3px rgba(74, 124, 89, 0.2));
    transition: all 0.3s ease;
}

[data-theme="jasmine-green"] .category-item:hover .category-icon {
    filter: drop-shadow(0 2px 6px rgba(74, 124, 89, 0.3));
    transform: scale(1.1);
}

/* 搜索框图标美化 */
[data-theme="jasmine-green"] .search-icon {
    color: var(--primary-color);
    filter: drop-shadow(0 1px 2px rgba(74, 124, 89, 0.2));
}

/* 内容标题装饰 */
[data-theme="jasmine-green"] .content-header h2 {
    position: relative;
    color: var(--text-primary);
    font-weight: 700;
}

[data-theme="jasmine-green"] .content-header h2::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* 网站计数器美化 */
[data-theme="jasmine-green"] .content-info {
    background: var(--jasmine-light);
    border: 1px solid var(--border-light);
    border-radius: 20px;
    padding: 6px 12px;
    color: var(--text-secondary);
    font-weight: 500;
    box-shadow: var(--shadow-light);
}

/* 标签云效果 */
[data-theme="jasmine-green"] .site-tags {
    position: relative;
}

[data-theme="jasmine-green"] .site-tags::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(74, 124, 89, 0.05), transparent);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

[data-theme="jasmine-green"] .site-card:hover .site-tags::before {
    opacity: 1;
}

/* 访问统计美化 */
[data-theme="jasmine-green"] .visit-count {
    background: var(--jasmine-medium);
    color: var(--text-secondary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 0.7rem;
    font-weight: 500;
    box-shadow: var(--shadow-light);
}

/* 帮助按钮特殊效果 */
[data-theme="jasmine-green"] .help-btn {
    position: relative;
    overflow: hidden;
}

[data-theme="jasmine-green"] .help-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(74, 124, 89, 0.2) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
}

[data-theme="jasmine-green"] .help-btn:hover::before {
    width: 100px;
    height: 100px;
}

/* 移动端优化 */
@media (max-width: 768px) {
    [data-theme="jasmine-green"] .site-card {
        border-radius: 12px;
    }
    
    [data-theme="jasmine-green"] .search-wrapper {
        border-radius: 12px;
    }
    
    [data-theme="jasmine-green"] .theme-dropdown-menu {
        border-radius: 12px;
    }
    
    [data-theme="jasmine-green"] .category-item {
        border-radius: 8px;
    }
    
    [data-theme="jasmine-green"] .action-btn {
        border-radius: 8px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    [data-theme="jasmine-green"] {
        --border-color: #a8d6a8;
        --border-hover: #7ba05b;
        --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.15);
        --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
        --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.25);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    [data-theme="jasmine-green"] * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

[data-theme="jasmine-green"] .custom-select-option.active {
    background-color: var(--jasmine-primary);
    color: white;
}

[data-theme="jasmine-green"] .custom-select-option.highlighted {
    background-color: var(--jasmine-accent);
}

[data-theme="jasmine-green"] .custom-select-arrow {
    color: var(--jasmine-secondary);
}

/* 原生select兼容样式 */
[data-theme="jasmine-green"] .filter-select {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(187, 233, 207, 0.4);
    color: var(--text-primary);
    backdrop-filter: blur(10px);
    /* 茉莉绿主题的下拉箭头 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2322c55e' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

[data-theme="jasmine-green"] .filter-select:focus {
    border-color: var(--jasmine-primary);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.15);
    background-color: var(--jasmine-white);
}

[data-theme="jasmine-green"] .filter-select:hover {
    background-color: var(--jasmine-white);
    border-color: var(--jasmine-primary);
}

[data-theme="jasmine-green"] .filter-select option {
    background-color: var(--jasmine-white);
    color: var(--text-primary);
}

/* 深邃海军蓝主题 */
[data-theme="navy-blue"] .custom-select-trigger {
    background-color: var(--surface-color);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="navy-blue"] .custom-select-trigger:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

[data-theme="navy-blue"] .custom-select.open .custom-select-trigger {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    background-color: var(--card-background);
}

[data-theme="navy-blue"] .custom-select-options {
    background-color: var(--card-background);
    border-color: var(--border-color);
    box-shadow: 0 20px 60px rgba(30, 58, 95, 0.2),
                0 8px 30px rgba(30, 58, 95, 0.1),
                0 2px 8px rgba(30, 58, 95, 0.05);
}

[data-theme="navy-blue"] .custom-select-option {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="navy-blue"] .custom-select-option:hover {
    background-color: var(--surface-hover);
}

[data-theme="navy-blue"] .custom-select-option.active {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="navy-blue"] .custom-select-option.highlighted {
    background-color: var(--surface-hover);
}

[data-theme="navy-blue"] .custom-select-arrow {
    color: var(--text-muted);
}

/* 原生select兼容样式 */
[data-theme="navy-blue"] .filter-select {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    /* 海军蓝主题的下拉箭头 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

[data-theme="navy-blue"] .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    background-color: var(--card-background);
}

[data-theme="navy-blue"] .filter-select option {
    background-color: var(--card-background);
    color: var(--text-primary);
}

/* 兼容性支持 - 原有的light主题 */
[data-theme="light"] .custom-select-trigger {
    background-color: var(--surface-color);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .custom-select-trigger:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

[data-theme="light"] .custom-select.open .custom-select-trigger {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    background-color: var(--card-background);
}

[data-theme="light"] .custom-select-options {
    background-color: var(--card-background);
    border-color: var(--border-color);
    box-shadow: var(--shadow-lg);
}

[data-theme="light"] .custom-select-option {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="light"] .custom-select-option:hover {
    background-color: var(--surface-hover);
}

[data-theme="light"] .custom-select-option.active {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="light"] .custom-select-option.highlighted {
    background-color: var(--surface-hover);
}

/* 原生select兼容样式 */
[data-theme="light"] .filter-select {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

[data-theme="light"] .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 兼容性支持 - 原有的dark主题 */
[data-theme="dark"] .custom-select-trigger {
    background-color: var(--surface-color);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .custom-select-trigger:hover {
    background-color: var(--surface-hover);
    border-color: var(--border-hover);
}

[data-theme="dark"] .custom-select.open .custom-select-trigger {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
    background-color: var(--card-background);
}

[data-theme="dark"] .custom-select-options {
    background-color: var(--card-background);
    border-color: var(--border-color);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3),
                0 8px 30px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .custom-select-option {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .custom-select-option:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark"] .custom-select-option.active {
    background-color: var(--primary-color);
    color: white;
}

[data-theme="dark"] .custom-select-option.highlighted {
    background-color: var(--surface-hover);
}

[data-theme="dark"] .custom-select-arrow {
    color: var(--text-muted);
}

/* 原生select兼容样式 */
[data-theme="dark"] .filter-select {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    /* 深色主题的下拉箭头 */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23c0c0c0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

[data-theme="dark"] .filter-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2);
}

[data-theme="dark"] .filter-select option {
    background-color: var(--card-background);
    color: var(--text-primary);
}

/* 滚动条在各主题下的样式 */
[data-theme="dark-obsidian"] ::-webkit-scrollbar-track,
[data-theme="navy-blue"] ::-webkit-scrollbar-track {
    background: var(--surface-color);
}

[data-theme="dark-obsidian"] ::-webkit-scrollbar-thumb,
[data-theme="navy-blue"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="dark-obsidian"] ::-webkit-scrollbar-thumb:hover,
[data-theme="navy-blue"] ::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

[data-theme="ivory-light"] ::-webkit-scrollbar-track,
[data-theme="jasmine-green"] ::-webkit-scrollbar-track {
    background: var(--surface-color);
}

[data-theme="ivory-light"] ::-webkit-scrollbar-thumb,
[data-theme="jasmine-green"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="ivory-light"] ::-webkit-scrollbar-thumb:hover,
[data-theme="jasmine-green"] ::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* 深色主题分类分组样式 */
[data-theme="dark-obsidian"] .category-group-title {
    color: var(--text-primary);
}

[data-theme="dark-obsidian"] .category-group-count {
    color: var(--text-secondary);
    background-color: var(--surface-color);
}

[data-theme="navy-blue"] .category-group-title {
    color: var(--text-primary);
}

[data-theme="navy-blue"] .category-group-count {
    color: var(--text-secondary);
    background-color: var(--surface-color);
}

/* 深色主题分组中的网站卡片样式与常规卡片保持一致 */
[data-theme="dark-obsidian"] .site-card-markdown,
[data-theme="navy-blue"] .site-card-markdown {
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
    border-left-color: var(--accent-color);
}

[data-theme="dark-obsidian"] .site-card-markdown:hover,
[data-theme="navy-blue"] .site-card-markdown:hover {
    border-left-color: var(--accent-hover);
    box-shadow: var(--shadow-md), 0 0 20px var(--accent-color-alpha);
}

/* 深色主题图标样式 */
[data-theme="dark-obsidian"] .site-icon {
    background-color: var(--surface-color);
}

[data-theme="dark-obsidian"] .site-icon-image {
    filter: brightness(0.9);
}

[data-theme="dark-obsidian"] .site-icon-fallback {
    color: var(--text-muted);
}

[data-theme="navy-blue"] .site-icon {
    background-color: var(--surface-color);
}

[data-theme="navy-blue"] .site-icon-image {
    filter: brightness(0.9);
}

[data-theme="navy-blue"] .site-icon-fallback {
    color: var(--text-muted);
}

/* 深色主题 Markdown 模态框 */
[data-theme="dark-obsidian"] .markdown-modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

[data-theme="dark-obsidian"] .markdown-modal-content {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="dark-obsidian"] .markdown-modal-header {
    border-bottom-color: var(--border-color);
}

[data-theme="dark-obsidian"] .markdown-modal-close:hover {
    background-color: var(--surface-hover);
}

[data-theme="dark-obsidian"] .markdown-modal-action-btn:hover {
    background-color: var(--surface-hover);
}

[data-theme="navy-blue"] .markdown-modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
}

[data-theme="navy-blue"] .markdown-modal-content {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

[data-theme="navy-blue"] .markdown-modal-header {
    border-bottom-color: var(--border-color);
}

[data-theme="navy-blue"] .markdown-modal-close:hover {
    background-color: var(--surface-hover);
}

[data-theme="navy-blue"] .markdown-modal-action-btn:hover {
    background-color: var(--surface-hover);
}

/* 深色主题 Markdown 卡片样式 */
[data-theme="dark-obsidian"] .site-type-badge {
    background-color: rgba(167, 139, 250, 0.15);
    color: #c4b5fd;
    border: 1px solid rgba(167, 139, 250, 0.25);
    font-weight: 600;
}

[data-theme="navy-blue"] .site-type-badge {
    background-color: var(--accent-color-alpha);
    color: var(--accent-color);
}

[data-theme="dark-obsidian"] .site-card-markdown {
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
    border-left-color: var(--accent-color);
}

[data-theme="dark-obsidian"] .site-card-markdown:hover {
    border-left-color: var(--accent-hover);
    box-shadow: var(--shadow-md), 0 0 20px var(--accent-color-alpha);
}

[data-theme="navy-blue"] .site-card-markdown {
    background: linear-gradient(135deg, var(--card-background) 0%, var(--surface-color) 100%);
    border-left-color: var(--accent-color);
}

[data-theme="navy-blue"] .site-card-markdown:hover {
    border-left-color: var(--accent-hover);
    box-shadow: var(--shadow-md), 0 0 20px var(--accent-color-alpha);
}

/* ==== 终极对比度修复 - 确保在所有情况下都生效 ==== */
/* 这个部分放在文件最后，具有最高优先级 */

/* 非深色主题强制使用黑色文字 */
[data-theme="ivory-light"] .theme-dropdown-menu .theme-option-name,
[data-theme="jasmine-green"] .theme-dropdown-menu .theme-option-name,
[data-theme="navy-blue"] .theme-dropdown-menu .theme-option-name {
    color: #000000 !important;
    font-weight: 600 !important;
}

/* 注意：星月流光蓝主题使用专门的白色文字规则，在下方定义 */

[data-theme="ivory-light"] .theme-dropdown-menu .theme-option-desc,
[data-theme="jasmine-green"] .theme-dropdown-menu .theme-option-desc,  
[data-theme="navy-blue"] .theme-dropdown-menu .theme-option-desc {
    color: #444444 !important;
}

/* 深色主题强制使用白色文字 */
[data-theme="dark-obsidian"] .theme-dropdown-menu .theme-option-name {
    color: #ffffff !important;
    font-weight: 600 !important;
}

[data-theme="dark-obsidian"] .theme-dropdown-menu .theme-option-desc {
    color: #cccccc !important;
}

/* 星月流光蓝主题强制使用荧光白色文字 */
[data-theme="star-moonlight-blue"] .theme-dropdown-menu .theme-option-name {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 0 8px rgba(0, 191, 255, 0.5) !important;
}

[data-theme="star-moonlight-blue"] .theme-dropdown-menu .theme-option-desc {
    color: #b8d4f0 !important;
    text-shadow: 0 0 4px rgba(0, 191, 255, 0.3) !important;
}

/* 最终保险 - 直接覆盖所有可能的选择器 */
.theme-dropdown-menu.show .theme-option-name {
    color: #000000 !important;
}

[data-theme="dark-obsidian"] .theme-dropdown-menu.show .theme-option-name {
    color: #ffffff !important;
}

[data-theme="star-moonlight-blue"] .theme-dropdown-menu.show .theme-option-name {
    color: #ffffff !important;
    text-shadow: 0 0 8px rgba(0, 191, 255, 0.5) !important;
}

[data-theme="star-moonlight-blue"] .theme-dropdown-menu.show .theme-option-desc {
    color: #b8d4f0 !important;
    text-shadow: 0 0 4px rgba(0, 191, 255, 0.3) !important;
}

/* 星月流光蓝主题 - 搜索筛选面板样式优化 */
[data-theme="star-moonlight-blue"] .search-filters {
    background: rgba(15, 20, 42, 0.95) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    border: 2px solid rgba(0, 191, 255, 0.4) !important;
    box-shadow: 
        0 20px 50px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(0, 191, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

[data-theme="star-moonlight-blue"] .search-filters-header {
    border-bottom: 1px solid rgba(0, 191, 255, 0.3) !important;
}

[data-theme="star-moonlight-blue"] .search-filters-title {
    color: var(--moonlight-white) !important;
    text-shadow: 0 0 8px rgba(0, 191, 255, 0.3) !important;
}

[data-theme="star-moonlight-blue"] .clear-filters-btn {
    color: var(--star-blue-primary) !important;
    background: rgba(0, 191, 255, 0.1) !important;
    border: 1px solid rgba(0, 191, 255, 0.2) !important;
}

[data-theme="star-moonlight-blue"] .clear-filters-btn:hover {
    background: rgba(0, 191, 255, 0.2) !important;
    color: #ffffff !important;
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.3) !important;
}

[data-theme="star-moonlight-blue"] .filter-label {
    color: var(--text-secondary) !important;
}

[data-theme="star-moonlight-blue"] .custom-select {
    background: rgba(30, 58, 138, 0.6) !important;
    border: 2px solid rgba(0, 191, 255, 0.3) !important;
}

[data-theme="star-moonlight-blue"] .custom-select:hover {
    border-color: rgba(0, 191, 255, 0.5) !important;
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.2) !important;
}

[data-theme="star-moonlight-blue"] .custom-select-text {
    color: var(--moonlight-white) !important;
}

[data-theme="star-moonlight-blue"] .custom-select-arrow {
    color: var(--star-blue-primary) !important;
}

[data-theme="star-moonlight-blue"] .custom-select-options {
    background: rgba(15, 20, 42, 0.95) !important;
    border: 2px solid rgba(0, 191, 255, 0.4) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(0, 191, 255, 0.2) !important;
}

[data-theme="star-moonlight-blue"] .custom-select-option {
    color: var(--moonlight-white) !important;
}

[data-theme="star-moonlight-blue"] .custom-select-option:hover {
    background: rgba(0, 191, 255, 0.2) !important;
    color: #ffffff !important;
}

[data-theme="star-moonlight-blue"] .custom-select-option.active {
    background: rgba(0, 191, 255, 0.3) !important;
    color: #ffffff !important;
}

[data-theme="star-moonlight-blue"] .tag-filter {
    background: rgba(30, 58, 138, 0.5) !important;
    border: 1px solid rgba(0, 191, 255, 0.3) !important;
    color: var(--text-secondary) !important;
}

[data-theme="star-moonlight-blue"] .tag-filter:hover {
    background: rgba(0, 191, 255, 0.2) !important;
    border-color: rgba(0, 191, 255, 0.5) !important;
    color: #ffffff !important;
}

[data-theme="star-moonlight-blue"] .tag-filter.active {
    background: rgba(0, 191, 255, 0.4) !important;
    border-color: var(--star-blue-primary) !important;
    color: #ffffff !important;
    box-shadow: 0 0 10px rgba(0, 191, 255, 0.3) !important;
}

/* 分类导航箭头在不同主题下的样式优化 */
[data-theme="dark-obsidian"] .category-arrow:hover,
[data-theme="navy-blue"] .category-arrow:hover {
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 1;
    transform: scale(1.1);
}

[data-theme="ivory-light"] .category-arrow:hover,
[data-theme="jasmine-green"] .category-arrow:hover {
    background-color: rgba(0, 0, 0, 0.05);
    opacity: 1;
    transform: scale(1.1);
}

/* 子分类列表在不同主题下的优化 */
[data-theme="dark-obsidian"] .subcategory-list,
[data-theme="navy-blue"] .subcategory-list {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.08), 
        rgba(59, 130, 246, 0.04));
    border-left-color: #60a5fa;
}

[data-theme="ivory-light"] .subcategory-list {
    background: linear-gradient(135deg, 
        rgba(59, 130, 246, 0.01), 
        rgba(59, 130, 246, 0.005));
}

[data-theme="jasmine-green"] .subcategory-list {
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.015), 
        rgba(5, 150, 105, 0.005));
}

/* 树状连接线在不同主题下的颜色适配 */
[data-theme="dark-obsidian"] .subcategory-list::before,
[data-theme="navy-blue"] .subcategory-list::before {
    background: linear-gradient(to bottom,
        rgba(148, 163, 184, 0.6) 0%,
        rgba(148, 163, 184, 0.6) 90%,
        transparent 100%);
}

[data-theme="ivory-light"] .subcategory-list::before {
    background: linear-gradient(to bottom,
        rgba(100, 116, 139, 0.4) 0%,
        rgba(100, 116, 139, 0.4) 90%,
        transparent 100%);
}

[data-theme="jasmine-green"] .subcategory-list::before {
    background: linear-gradient(to bottom,
        rgba(5, 150, 105, 0.5) 0%,
        rgba(5, 150, 105, 0.5) 90%,
        transparent 100%);
}

/* 子分类悬停效果在不同主题下的优化 */
[data-theme="dark-obsidian"] .subcategory-list .category-link:hover {
    background-color: rgba(59, 130, 246, 0.12);
    border-color: rgba(96, 165, 250, 0.3);
}

[data-theme="navy-blue"] .subcategory-list .category-link:hover {
    background-color: rgba(59, 130, 246, 0.12);
    border-color: rgba(96, 165, 250, 0.3);
}

[data-theme="ivory-light"] .subcategory-list .category-link:hover {
    background-color: rgba(59, 130, 246, 0.08);
    border-color: rgba(59, 130, 246, 0.2);
}

[data-theme="jasmine-green"] .subcategory-list .category-link:hover {
    background-color: rgba(5, 150, 105, 0.08);
    border-color: rgba(16, 185, 129, 0.3);
}

/* 水平连接线和节点指示器的主题适配 */
[data-theme="dark-obsidian"] .subcategory-list .category-link::before,
[data-theme="navy-blue"] .subcategory-list .category-link::before {
    background-color: rgba(148, 163, 184, 0.4);
}

[data-theme="dark-obsidian"] .subcategory-list .category-link::after,
[data-theme="navy-blue"] .subcategory-list .category-link::after {
    background-color: rgba(148, 163, 184, 0.5);
}

[data-theme="ivory-light"] .subcategory-list .category-link::before {
    background-color: rgba(100, 116, 139, 0.4);
}

[data-theme="ivory-light"] .subcategory-list .category-link::after {
    background-color: rgba(100, 116, 139, 0.5);
}

[data-theme="jasmine-green"] .subcategory-list .category-link::before {
    background-color: rgba(5, 150, 105, 0.4);
}

[data-theme="jasmine-green"] .subcategory-list .category-link::after {
    background-color: rgba(5, 150, 105, 0.5);
}

/* 悬停状态的主题适配 */
[data-theme="dark-obsidian"] .subcategory-list .category-link:hover::before,
[data-theme="navy-blue"] .subcategory-list .category-link:hover::before {
    background-color: #60a5fa;
}

[data-theme="dark-obsidian"] .subcategory-list .category-link:hover::after,
[data-theme="navy-blue"] .subcategory-list .category-link:hover::after {
    background-color: #60a5fa;
}

[data-theme="jasmine-green"] .subcategory-list .category-link:hover::before,
[data-theme="jasmine-green"] .subcategory-list .category-link:hover::after {
    background-color: #10b981;
}

/* 分类链接在各主题下的统一优化 */
[data-theme="dark-obsidian"] .category-link:hover,
[data-theme="navy-blue"] .category-link:hover {
    box-shadow: 0 2px 6px rgba(255, 255, 255, 0.05);
}

[data-theme="ivory-light"] .category-link:hover,
[data-theme="jasmine-green"] .category-link:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 分类计数器在不同主题下的优化 */
[data-theme="dark-obsidian"] .category-count,
[data-theme="navy-blue"] .category-count {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--text-muted);
}

[data-theme="ivory-light"] .category-count,
[data-theme="jasmine-green"] .category-count {
    background-color: var(--surface-hover);
    color: var(--text-muted);
}

/* 滚动条在不同主题下的优化 */
[data-theme="dark-obsidian"] .sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark-obsidian"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

[data-theme="navy-blue"] .sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
}

[data-theme="navy-blue"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

[data-theme="ivory-light"] .sidebar::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="ivory-light"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

[data-theme="jasmine-green"] .sidebar::-webkit-scrollbar-thumb {
    background: var(--border-color);
}

[data-theme="jasmine-green"] .sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 为茉莉绿主题添加特殊的分类效果 */
[data-theme="jasmine-green"] .category-link.active {
    background: linear-gradient(135deg, #059669, #10b981);
    box-shadow: 0 4px 8px rgba(5, 150, 105, 0.15);
}

[data-theme="jasmine-green"] .subcategory-list .category-link.active {
    background: linear-gradient(135deg, #059669, #10b981);
    box-shadow: 0 2px 6px rgba(5, 150, 105, 0.2);
}

/* 外部链接卡片在不同主题下的贴合设计 */

/* 日光象牙白主题 - 象牙白相近的浅灰色 */
[data-theme="ivory-light"] .site-card-external {
    border-left: 2px solid #e8e6e3;
}

[data-theme="ivory-light"] .site-card-external:hover {
    border-left-color: #d4d1cc;
    box-shadow: var(--shadow-md), 0 0 15px rgba(232, 230, 227, 0.12);
}

/* 夜月玄玉黑主题 - 冷色调银灰 */
[data-theme="dark-obsidian"] .site-card-external {
    border-left-color: #64748b;
}

[data-theme="dark-obsidian"] .site-card-external:hover {
    border-left-color: #475569;
    box-shadow: var(--shadow-md), 0 0 15px rgba(100, 116, 139, 0.12);
}

/* 清雅茉莉绿主题 - 自然绿色 */
[data-theme="jasmine-green"] .site-card-external {
    border-left-color: #16a34a;
}

[data-theme="jasmine-green"] .site-card-external:hover {
    border-left-color: #15803d;
    box-shadow: var(--shadow-md), 0 0 15px rgba(22, 163, 74, 0.1);
}

/* 深邃海军蓝主题 - 浅白色 */
[data-theme="navy-blue"] .site-card-external {
    border-left: 2px solid #e2e8f0;
}

[data-theme="navy-blue"] .site-card-external:hover {
    border-left-color: #f1f5f9;
    box-shadow: var(--shadow-md), 0 0 15px rgba(226, 232, 240, 0.15);
}

/* 兼容性主题 */
[data-theme="light"] .site-card-external {
    border-left: 2px solid #e8e6e3;
}

[data-theme="light"] .site-card-external:hover {
    border-left-color: #d4d1cc;
    box-shadow: var(--shadow-md), 0 0 15px rgba(232, 230, 227, 0.12);
}

[data-theme="dark"] .site-card-external {
    border-left-color: #64748b;
}

[data-theme="dark"] .site-card-external:hover {
    border-left-color: #475569;
    box-shadow: var(--shadow-md), 0 0 15px rgba(100, 116, 139, 0.12);
}

/* Markdown卡片在不同主题下的优化 */

/* 日光象牙白主题 - 稍深的灰色用于Markdown */
[data-theme="ivory-light"] .site-card-markdown {
    border-left: 2px solid #d4d1cc;
}

[data-theme="ivory-light"] .site-card-markdown:hover {
    border-left-color: #b8b4af;
    box-shadow: var(--shadow-md), 0 0 15px rgba(212, 209, 204, 0.12);
}

/* 夜月玄玉黑主题 - 淡紫色 */
[data-theme="dark-obsidian"] .site-card-markdown {
    border-left-color: #a78bfa;
}

[data-theme="dark-obsidian"] .site-card-markdown:hover {
    border-left-color: #8b5cf6;
    box-shadow: var(--shadow-md), 0 0 15px rgba(167, 139, 250, 0.12);
}

/* 清雅茉莉绿主题 - 深绿色 */
[data-theme="jasmine-green"] .site-card-markdown {
    border-left-color: #22c55e;
}

[data-theme="jasmine-green"] .site-card-markdown:hover {
    border-left-color: #16a34a;
    box-shadow: var(--shadow-md), 0 0 15px rgba(34, 197, 94, 0.1);
}

/* 深邃海军蓝主题 - 温暖的浅黄色 */
[data-theme="navy-blue"] .site-card-markdown {
    border-left: 2px solid #fbbf24;
}

[data-theme="navy-blue"] .site-card-markdown:hover {
    border-left-color: #f59e0b;
    box-shadow: var(--shadow-md), 0 0 15px rgba(251, 191, 36, 0.15);
}

/* 兼容性主题的Markdown卡片 */
[data-theme="light"] .site-card-markdown {
    border-left: 2px solid #d4d1cc;
}

[data-theme="light"] .site-card-markdown:hover {
    border-left-color: #b8b4af;
    box-shadow: var(--shadow-md), 0 0 15px rgba(212, 209, 204, 0.12);
}

[data-theme="dark"] .site-card-markdown {
    border-left-color: #a78bfa;
}

[data-theme="dark"] .site-card-markdown:hover {
    border-left-color: #8b5cf6;
    box-shadow: var(--shadow-md), 0 0 15px rgba(167, 139, 250, 0.12);
}

/* 星月流光蓝主题 - 浩瀚星空主题 */
[data-theme="star-moonlight-blue"] {
    /* 基础背景色系 - 深邃星空蓝色调 */
    --background-color: #0a1428;
    --surface-color: rgba(30, 42, 74, 0.4);
    --surface-hover: rgba(44, 79, 139, 0.6);
    --card-background: rgba(30, 58, 138, 0.4);
    
    /* 文字色系 - 星光白色调 */
    --text-primary: #f0f8ff;
    --text-secondary: #b8d4f0;
    --text-muted: #94a3b8;
    
    /* 边框色系 - 流光蓝边框 */
    --border-color: rgba(0, 191, 255, 0.3);
    --border-hover: rgba(0, 191, 255, 0.5);
    
    /* 阴影系统 - 星光阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 10px rgba(0, 191, 255, 0.1);
    --shadow-md: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 25px rgba(0, 191, 255, 0.1);
    --shadow-lg: 0 16px 40px rgba(0, 0, 0, 0.3), 0 0 40px rgba(30, 144, 255, 0.2);
    
    /* 主题色系 - 流光蓝系 */
    --primary-color: #00bfff;
    --primary-hover: #1e90ff;
    --accent-color: #4169e1;
    --accent-hover: #6495ed;
    --accent-color-alpha: rgba(0, 191, 255, 0.1);
    
    /* 状态色系 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #00bfff;
    
    /* 转换时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;

    /* 收藏按钮主题适配 */
    --favorite-btn-bg: rgba(255, 255, 255, 0.9);
    --favorite-btn-bg-hover: rgba(255, 255, 255, 1);
    --favorite-btn-active: var(--warning-color);
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    
    /* 星月流光蓝主题专属变量 */
    --star-blue-primary: #00bfff;
    --star-blue-secondary: #1e90ff;
    --star-blue-accent: #4169e1;
    --star-blue-light: rgba(0, 191, 255, 0.2);
    --star-blue-soft: rgba(30, 144, 255, 0.1);
    --moonlight-white: #f0f8ff;
    --star-glow: rgba(255, 255, 255, 0.8);
}

/* 星空背景渐变效果 */
[data-theme="star-moonlight-blue"] body {
    background: linear-gradient(135deg, #0a1428 0%, #1e2a4a 20%, #2c4f8b 40%, #4682b4 60%, #1e3a8a 80%, #0f172a 100%);
    background-attachment: fixed;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* 星空背景容器 */
[data-theme="star-moonlight-blue"] body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -3;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="star" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:white;stop-opacity:1" /><stop offset="100%" style="stop-color:white;stop-opacity:0" /></radialGradient></defs><circle cx="10" cy="20" r="0.5" fill="url(%23star)" opacity="0.8" /><circle cx="80" cy="10" r="1" fill="url(%23star)" opacity="0.6" /><circle cx="30" cy="70" r="0.8" fill="url(%23star)" opacity="0.7" /><circle cx="90" cy="40" r="0.3" fill="url(%23star)" opacity="0.9" /><circle cx="60" cy="90" r="1.2" fill="url(%23star)" opacity="0.5" /><circle cx="20" cy="50" r="0.4" fill="url(%23star)" opacity="0.8" /><circle cx="70" cy="30" r="0.7" fill="url(%23star)" opacity="0.6" /><circle cx="40" cy="10" r="0.6" fill="url(%23star)" opacity="0.7" /><circle cx="85" cy="70" r="0.9" fill="url(%23star)" opacity="0.5" /><circle cx="15" cy="80" r="0.5" fill="url(%23star)" opacity="0.8" /></svg>');
    animation: starfield-twinkle 4s ease-in-out infinite;
}

/* 银河系背景 */
[data-theme="star-moonlight-blue"] body::after {
    content: '';
    position: fixed;
    top: 0;
    left: -15%;
    width: 130%;
    height: 100%;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 191, 255, 0.08) 10%,
        rgba(30, 144, 255, 0.15) 25%, 
        rgba(65, 105, 225, 0.2) 40%, 
        rgba(100, 149, 237, 0.25) 50%,
        rgba(65, 105, 225, 0.2) 60%,
        rgba(30, 144, 255, 0.15) 75%,
        rgba(0, 191, 255, 0.08) 90%,
        transparent 100%);
    transform: rotate(-12deg);
    z-index: -2;
    animation: milky-way-flow 25s infinite linear;
    pointer-events: none;
}

@keyframes starfield-twinkle {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

@keyframes milky-way-flow {
    0% { transform: rotate(-12deg) translateX(-100px) translateY(20px); }
    100% { transform: rotate(-12deg) translateX(100px) translateY(-20px); }
}

/* 导航栏星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .navbar-custom {
    background: rgba(30, 58, 138, 0.4);
    backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 2px solid rgba(0, 191, 255, 0.4);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3), 0 0 25px rgba(0, 191, 255, 0.1);
}

/* 品牌文字流光效果 */
[data-theme="star-moonlight-blue"] .brand-text {
    background: linear-gradient(135deg, #ffffff, #00bfff, #1e90ff, #4169e1);
    background-size: 200% 200%;
    animation: star-blue-gradient 3s ease-in-out infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 40px rgba(0, 191, 255, 0.6);
}

@keyframes star-blue-gradient {
    0%, 100% { background-position: 0% 50%; filter: brightness(1); }
    50% { background-position: 100% 50%; filter: brightness(1.3); }
}

/* 搜索框星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .search-input {
    background: rgba(30, 58, 138, 0.4);
    border: 2px solid rgba(0, 191, 255, 0.4);
    color: var(--moonlight-white);
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 0 20px rgba(0, 191, 255, 0.2);
    transition: all 0.4s ease;
}

[data-theme="star-moonlight-blue"] .search-input:focus {
    background: rgba(30, 58, 138, 0.6);
    border-color: var(--star-blue-primary);
    box-shadow: 0 0 40px rgba(0, 191, 255, 0.5), 0 10px 30px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 0 30px rgba(30, 144, 255, 0.3);
}

[data-theme="star-moonlight-blue"] .search-input::placeholder {
    color: var(--text-secondary);
}

[data-theme="star-moonlight-blue"] .search-icon {
    color: var(--star-blue-primary);
    text-shadow: 0 0 10px rgba(0, 191, 255, 0.5);
}

[data-theme="star-moonlight-blue"] .search-icon:hover {
    color: #fff;
    text-shadow: 0 0 20px #00bfff, 0 0 30px #1e90ff;
    transform: scale(1.1);
}

/* 侧边栏星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .sidebar {
    background: rgba(15, 20, 42, 0.6);
    backdrop-filter: blur(20px);
    border-right: 2px solid rgba(0, 191, 255, 0.3);
}

[data-theme="star-moonlight-blue"] .category-name:hover {
    color: #fff;
    background: var(--star-blue-light);
    transform: translateX(4px);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="star-moonlight-blue"] .category-name.active {
    background: linear-gradient(135deg, var(--star-blue-primary), var(--star-blue-secondary));
    color: white;
    box-shadow: 0 4px 12px rgba(0, 191, 255, 0.4), 0 0 20px rgba(30, 144, 255, 0.3);
}

/* 卡片系统星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .site-card {
    background: var(--card-background);
    border: 2px solid rgba(0, 191, 255, 0.3);
    backdrop-filter: blur(20px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 0 25px rgba(0, 191, 255, 0.1);
    transition: all 0.4s ease;
}

[data-theme="star-moonlight-blue"] .site-card:hover {
    background: rgba(30, 58, 138, 0.7);
    border-color: var(--star-blue-primary);
    transform: translateY(-10px) scale(1.03);
    box-shadow: 0 25px 60px rgba(0, 191, 255, 0.4), 0 12px 35px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 0 40px rgba(30, 144, 255, 0.3);
}

/* 卡片流光扫过效果 */
[data-theme="star-moonlight-blue"] .site-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 191, 255, 0.3), rgba(30, 144, 255, 0.2), transparent);
    transition: left 0.8s ease;
    pointer-events: none;
    z-index: 1;
}

[data-theme="star-moonlight-blue"] .site-card:hover::before {
    left: 100%;
}

/* 网站图标星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .site-icon {
    background: var(--star-blue-soft);
    border: 1px solid rgba(0, 191, 255, 0.3);
    transition: all 0.4s ease;
}

[data-theme="star-moonlight-blue"] .site-card:hover .site-icon {
    background: var(--star-blue-light);
    transform: scale(1.15);
    box-shadow: 0 0 25px #00bfff, 0 0 40px #1e90ff, 0 0 55px #4169e1;
}

/* 标签系统星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .site-tag {
    background: var(--star-blue-soft);
    color: var(--star-blue-primary);
    border: 1px solid rgba(0, 191, 255, 0.2);
    transition: all 0.3s ease;
}

[data-theme="star-moonlight-blue"] .site-tag:hover {
    background: var(--star-blue-primary);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.5);
}

/* 按钮系统星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .action-btn {
    color: var(--star-blue-primary);
    transition: all 0.3s ease;
}

[data-theme="star-moonlight-blue"] .action-btn:hover {
    background: var(--star-blue-light);
    color: #fff;
    transform: scale(1.05);
    text-shadow: 0 0 15px #00bfff;
}

[data-theme="star-moonlight-blue"] .theme-selector-btn {
    background: rgba(30, 58, 138, 0.4);
    border: 2px solid rgba(0, 191, 255, 0.4);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 20px rgba(0, 191, 255, 0.2);
}

[data-theme="star-moonlight-blue"] .theme-selector-btn:hover {
    background: rgba(30, 58, 138, 0.6);
    border-color: var(--star-blue-primary);
    box-shadow: 0 0 30px rgba(0, 191, 255, 0.3);
}

/* 搜索结果星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .search-result-item:hover {
    background: var(--star-blue-light);
    border-radius: 8px;
    transform: translateX(4px);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
}

[data-theme="star-moonlight-blue"] .search-result-tag {
    background: var(--star-blue-soft);
    color: var(--star-blue-primary);
    border: 1px solid rgba(0, 191, 255, 0.2);
}

/* 滚动条星月流光蓝主题 */
[data-theme="star-moonlight-blue"] ::-webkit-scrollbar-track {
    background: rgba(15, 20, 42, 0.3);
}

[data-theme="star-moonlight-blue"] ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--star-blue-primary), var(--star-blue-secondary));
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 191, 255, 0.3);
}

[data-theme="star-moonlight-blue"] ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--star-blue-secondary), var(--star-blue-accent));
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.5);
}

/* Markdown卡片星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .site-card-markdown {
    border-left: 2px solid var(--star-blue-primary);
}

[data-theme="star-moonlight-blue"] .site-card-markdown:hover {
    border-left-color: var(--star-blue-secondary);
    box-shadow: var(--shadow-md), 0 0 15px rgba(0, 191, 255, 0.15);
}

/* Markdown模态框星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .markdown-modal-backdrop {
    background: rgba(10, 20, 40, 0.7);
    backdrop-filter: blur(12px);
}

[data-theme="star-moonlight-blue"] .markdown-modal-content {
    background: rgba(30, 42, 74, 0.9);
    border: 2px solid rgba(0, 191, 255, 0.4);
    box-shadow: 0 24px 64px rgba(0, 0, 0, 0.4),
                0 12px 32px rgba(0, 191, 255, 0.2),
                0 0 40px rgba(30, 144, 255, 0.1);
    backdrop-filter: blur(20px);
}

[data-theme="star-moonlight-blue"] .markdown-modal-header {
    border-bottom: 2px solid rgba(0, 191, 255, 0.3);
    background: rgba(15, 20, 42, 0.8);
}

[data-theme="star-moonlight-blue"] .markdown-modal-action-btn:hover,
[data-theme="star-moonlight-blue"] .markdown-modal-close:hover {
    background: rgba(0, 191, 255, 0.2);
    color: var(--star-blue-primary);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
}

/* 空状态星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .empty-state {
    color: var(--text-secondary);
}

[data-theme="star-moonlight-blue"] .empty-state i {
    color: var(--star-blue-primary);
    text-shadow: 0 0 20px rgba(0, 191, 255, 0.3);
}

/* 加载动画星月流光蓝主题 */
[data-theme="star-moonlight-blue"] .loading-spinner .spinner-border {
    color: var(--star-blue-primary);
    filter: drop-shadow(0 0 10px rgba(0, 191, 255, 0.5));
}

/* 主题选择器下拉菜单星月流光蓝主题特殊样式 */
[data-theme="star-moonlight-blue"] .theme-dropdown-menu {
    background: rgba(15, 20, 42, 0.9);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(0, 191, 255, 0.3);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 191, 255, 0.2);
}

/* 星月流光蓝主题使用浅色文字 - 最高优先级 */
html[data-theme="star-moonlight-blue"] .theme-option,
body[data-theme="star-moonlight-blue"] .theme-option {
    color: var(--moonlight-white) !important;
}

html[data-theme="star-moonlight-blue"] .theme-option:hover,
body[data-theme="star-moonlight-blue"] .theme-option:hover {
    background: var(--star-blue-light);
    transform: translateX(4px);
    box-shadow: 0 0 15px rgba(0, 191, 255, 0.3);
    color: #ffffff !important;
}

html[data-theme="star-moonlight-blue"] .theme-option-name,
body[data-theme="star-moonlight-blue"] .theme-option-name {
    color: #ffffff !important;
}

html[data-theme="star-moonlight-blue"] .theme-option-desc,
body[data-theme="star-moonlight-blue"] .theme-option-desc {
    color: #b8d4f0 !important;
}

html[data-theme="star-moonlight-blue"] .theme-option:hover .theme-option-name,
body[data-theme="star-moonlight-blue"] .theme-option:hover .theme-option-name {
    color: #ffffff !important;
}

html[data-theme="star-moonlight-blue"] .theme-option:hover .theme-option-desc,
body[data-theme="star-moonlight-blue"] .theme-option:hover .theme-option-desc {
    color: #ffffff !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    [data-theme="star-moonlight-blue"] body::after {
        width: 150%;
        left: -25%;
    }
    
    [data-theme="star-moonlight-blue"] .site-card:hover {
        transform: translateY(-5px) scale(1.02);
    }
}