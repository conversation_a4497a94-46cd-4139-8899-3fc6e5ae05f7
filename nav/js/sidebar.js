/**
 * 侧边栏管理器
 * 负责处理分类导航、展开折叠状态和移动端适配
 */
class SidebarManager {
    constructor(navApp) {
        this.navApp = navApp;
        this.sidebar = null;
        this.sidebarOverlay = null;
        this.mobileMenuToggle = null;
        this.categoryList = null;
        this.currentCategory = 'all-categories'; // 默认显示全部分类
        this.expandedCategories = new Set();
        this.isMobileSidebarOpen = false;

        // 移除动画相关控制以提升性能

        // 立即加载保存的状态，确保在NavApp同步时就有正确的分类
        this.loadExpandedStateSync();

        this.init();
    }
    
    /**
     * 初始化侧边栏管理器
     */
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupSidebarElements();
                this.bindEvents();
                this.loadExpandedState();
            });
        } else {
            this.setupSidebarElements();
            this.bindEvents();
            this.loadExpandedState();
        }
    }
    
    /**
     * 设置侧边栏元素
     */
    setupSidebarElements() {
        this.sidebar = document.getElementById('sidebar');
        this.sidebarOverlay = document.getElementById('sidebarOverlay');
        this.mobileMenuToggle = document.getElementById('mobileMenuToggle');
        this.categoryList = document.getElementById('categoryList');
        
        if (!this.sidebar || !this.categoryList) {
            console.error('Sidebar elements not found');
            return;
        }
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 移动端菜单切换
        if (this.mobileMenuToggle) {
            this.mobileMenuToggle.addEventListener('click', () => {
                this.toggleMobileSidebar();
            });
        }
        
        // 遮罩层点击关闭
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }
        
        // 响应式变化监听
        this.setupResponsiveListeners();
        
        // 键盘导航
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });
    }
    
    /**
     * 设置响应式监听器
     */
    setupResponsiveListeners() {
        const mediaQuery = window.matchMedia('(max-width: 767px)');
        const smallScreenQuery = window.matchMedia('(max-width: 479px)');
        const extraSmallScreenQuery = window.matchMedia('(max-width: 359px)');

        mediaQuery.addEventListener('change', (e) => {
            if (!e.matches && this.isMobileSidebarOpen) {
                // 从移动端切换到桌面端时关闭移动侧边栏
                this.closeMobileSidebar();
            }
            this.updateMobileMenuVisibility();
        });

        // 小屏幕和超小屏幕监听
        smallScreenQuery.addEventListener('change', () => {
            this.updateMobileMenuVisibility();
        });

        extraSmallScreenQuery.addEventListener('change', () => {
            this.updateMobileMenuVisibility();
        });

        // 窗口大小变化监听
        window.addEventListener('resize', throttle(() => {
            this.handleResize();
        }, 250));

        // 初始化时检查移动端菜单可见性
        this.updateMobileMenuVisibility();

        // 定期检查移动端菜单按钮的可见性（防止被其他元素遮挡）
        setInterval(() => {
            this.ensureMobileMenuVisibility();
        }, 2000);
    }
    
    /**
     * 处理窗口大小变化
     */
    handleResize() {
        if (window.innerWidth > 767 && this.isMobileSidebarOpen) {
            this.closeMobileSidebar();
        }
        this.updateMobileMenuVisibility();
    }

    /**
     * 更新移动端菜单按钮的可见性和样式
     */
    updateMobileMenuVisibility() {
        if (!this.mobileMenuToggle) return;

        const isMobile = window.innerWidth <= 767;
        const isSmallScreen = window.innerWidth <= 479;
        const isExtraSmallScreen = window.innerWidth <= 359;

        if (isMobile) {
            // 移动端显示菜单按钮
            this.mobileMenuToggle.style.display = 'flex';

            // 根据屏幕大小调整按钮样式
            if (isExtraSmallScreen) {
                this.mobileMenuToggle.style.width = '44px';
                this.mobileMenuToggle.style.height = '44px';
                this.mobileMenuToggle.style.fontSize = '18px';
            } else if (isSmallScreen) {
                this.mobileMenuToggle.style.width = '40px';
                this.mobileMenuToggle.style.height = '40px';
                this.mobileMenuToggle.style.fontSize = '16px';
            } else {
                this.mobileMenuToggle.style.width = '';
                this.mobileMenuToggle.style.height = '';
                this.mobileMenuToggle.style.fontSize = '';
            }

            // 确保按钮在最右侧且可见
            this.mobileMenuToggle.style.order = '999';
            this.mobileMenuToggle.style.flexShrink = '0';
            this.mobileMenuToggle.style.zIndex = '1001';

            console.log(`移动端菜单按钮已显示 (屏幕宽度: ${window.innerWidth}px)`);
        } else {
            // 桌面端隐藏菜单按钮
            this.mobileMenuToggle.style.display = 'none';
        }
    }

    /**
     * 确保移动端菜单按钮始终可见
     */
    ensureMobileMenuVisibility() {
        if (!this.mobileMenuToggle || window.innerWidth > 767) return;

        const rect = this.mobileMenuToggle.getBoundingClientRect();
        const viewportWidth = window.innerWidth;

        // 检查按钮是否在视口内
        if (rect.right > viewportWidth || rect.left < 0) {
            console.warn('移动端菜单按钮可能被遮挡，正在修复...');

            // 强制重新定位按钮
            this.mobileMenuToggle.style.position = 'relative';
            this.mobileMenuToggle.style.right = '0';
            this.mobileMenuToggle.style.marginLeft = 'auto';

            // 确保父容器有正确的布局
            const navbarActions = this.mobileMenuToggle.closest('.navbar-actions');
            if (navbarActions) {
                navbarActions.style.display = 'flex';
                navbarActions.style.flexShrink = '0';
                navbarActions.style.alignItems = 'center';
            }
        }

        // 检查按钮是否真的可见
        const computedStyle = window.getComputedStyle(this.mobileMenuToggle);
        if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') {
            console.warn('移动端菜单按钮被隐藏，正在强制显示...');
            this.mobileMenuToggle.style.display = 'flex !important';
            this.mobileMenuToggle.style.visibility = 'visible !important';
        }
    }
    
    /**
     * 处理键盘导航
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyboardNavigation(e) {
        // ESC键关闭移动端侧边栏（只在侧边栏打开时生效）
        if (e.key === 'Escape' && this.isMobileSidebarOpen) {
            e.preventDefault();
            this.closeMobileSidebar();
        }
    }
    
    /**
     * 渲染侧边栏分类（增强版本）
     * @param {Array} categories 分类数据
     */
    renderCategories(categories) {
        if (!this.categoryList || !categories) return;

        // 添加"最近访问"分类选项
        const frequentCount = this.getFrequentSitesCount();
        let categoryHtml = `
            <li class="category-item">
                <a href="#" class="category-link ${this.currentCategory === 'frequent' ? 'active' : ''}"
                   data-category-id="frequent"
                   data-has-children="false"
                   data-level="0"
                   role="button"
                   tabindex="0"
                   aria-label="最近访问"
                   title="${frequentCount === 0 ? '点击网站卡片后，它们将出现在这里' : `${frequentCount} 个最近访问网站`}">
                    <span class="category-icon" aria-hidden="true">⭐</span>
                    <span class="category-name">最近访问</span>
                    <span class="category-count">${frequentCount}</span>
                </a>
            </li>
        `;

        // 添加"全部分类"选项
        categoryHtml += `
            <li class="category-item">
                <a href="#" class="category-link ${this.currentCategory === 'all-categories' ? 'active' : ''}"
                   data-category-id="all-categories"
                   data-has-children="false"
                   data-level="0"
                   role="button"
                   tabindex="0"
                   aria-label="全部分类">
                    <span class="category-icon" aria-hidden="true">🌐</span>
                    <span class="category-name">全部分类</span>
                    <span class="category-count">${this.getTotalSitesCount()}</span>
                </a>
            </li>
        `;

        // 添加分隔线
        categoryHtml += `
            <li class="category-divider">
                <hr class="category-separator">
            </li>
        `;

        // 添加其他分类
        categoryHtml += categories.map(category => {
            return this.renderCategoryItem(category);
        }).join('');

        this.categoryList.innerHTML = categoryHtml;

        // 绑定分类点击事件
        this.bindCategoryEvents();

        // 恢复保存的展开状态
        this.restoreExpandedState();

        // 设置默认选中状态
        // 如果主应用有当前分类，使用主应用的分类
        const appCurrentCategory = this.navApp ? this.navApp.currentCategory : this.currentCategory;
        this.setActiveCategory(appCurrentCategory);

        console.log('Sidebar categories rendered (performance optimized)');
    }
    
    /**
     * 设置活动分类（性能优化版本）
     * @param {string} categoryId 分类ID
     * @param {boolean} fromScroll 是否来自滚动事件
     */
    setActiveCategory(categoryId, fromScroll = false) {
        // 如果分类没有变化，直接返回
        if (this.currentCategory === categoryId) return;

        // 移除所有活动状态
        this.categoryList.querySelectorAll('.category-link').forEach(link => {
            link.classList.remove('active');
        });

        // 设置新的活动状态
        const activeLink = this.categoryList.querySelector(`[data-category-id="${categoryId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
            this.currentCategory = categoryId;

            // 只有在非滚动事件时才保存状态
            if (!fromScroll) {
                this.saveCurrentCategory();
            }

            // 确保父分类展开
            this.ensureParentExpanded(activeLink);

            // 只有在非滚动事件时才滚动到可视区域
            if (!fromScroll) {
                this.scrollToActiveCategory(activeLink);
            }
        }
    }

    /**
     * 直接设置活动分类（无动画、无滚动，用于初始化）
     * @param {string} categoryId 分类ID
     */
    setActiveCategoryDirectly(categoryId) {
        // 移除所有活动状态
        this.categoryList.querySelectorAll('.category-link').forEach(link => {
            link.classList.remove('active');
        });

        // 设置新的活动状态
        const activeLink = this.categoryList.querySelector(`[data-category-id="${categoryId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
            this.currentCategory = categoryId;

            // 确保父分类展开（无动画）
            this.ensureParentExpandedDirectly(activeLink);
        }
    }

    /**
     * 确保父分类展开
     * @param {Element} activeLink 活动分类链接
     */
    ensureParentExpanded(activeLink) {
        let parentSubcategoryList = activeLink.closest('.subcategory-list');
        let stateChanged = false;
        
        while (parentSubcategoryList) {
            const parentCategoryItem = parentSubcategoryList.previousElementSibling;
            if (parentCategoryItem && parentCategoryItem.classList.contains('category-link')) {
                const parentCategoryId = parentCategoryItem.dataset.categoryId;
                if (!this.expandedCategories.has(parentCategoryId)) {
                    this.expandedCategories.add(parentCategoryId);
                    this.updateCategoryExpanded(parentCategoryId, true);
                    stateChanged = true;
                }
            }
            
            parentSubcategoryList = parentSubcategoryList.closest('.category-item')?.closest('.subcategory-list');
        }
        
        // 如果状态发生变化，保存展开状态
        if (stateChanged) {
            this.saveExpandedState();
        }
    }

    /**
     * 确保父分类展开（直接设置，无动画）
     * @param {Element} activeLink 活动分类链接
     */
    ensureParentExpandedDirectly(activeLink) {
        let parentSubcategoryList = activeLink.closest('.subcategory-list');

        while (parentSubcategoryList) {
            const parentCategoryItem = parentSubcategoryList.previousElementSibling;
            if (parentCategoryItem && parentCategoryItem.classList.contains('category-link')) {
                const parentCategoryId = parentCategoryItem.dataset.categoryId;
                if (!this.expandedCategories.has(parentCategoryId)) {
                    this.expandedCategories.add(parentCategoryId);
                    // 直接设置展开状态，不触发动画
                    this.setExpandedStateDirectly(parentCategoryId, true);
                }
            }

            parentSubcategoryList = parentSubcategoryList.closest('.category-item')?.closest('.subcategory-list');
        }
    }

    /**
     * 切换移动端侧边栏
     */
    toggleMobileSidebar() {
        if (this.isMobileSidebarOpen) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    /**
     * 增强的分类项渲染（支持动画延迟）
     * @param {Object} category 分类数据
     * @param {number} level 层级深度
     * @returns {string} HTML字符串
     */
    renderCategoryItem(category, level = 0) {
        const hasChildren = category.children && category.children.length > 0;
        const isExpanded = this.expandedCategories.has(category.id);
        const isActive = this.currentCategory === category.id;
        
        // 计算分类中的网站数量
        const siteCount = this.getCategorySiteCount(category.id);
        const countDisplay = siteCount > 0 ? `<span class="category-count">${siteCount}</span>` : '';

        // 添加层级样式和动画延迟
        const levelClass = level > 0 ? 'subcategory-item' : 'category-item';
        const indentStyle = level > 0 ? `style="padding-left: ${12 + level * 16}px;"` : '';

        let html = `
            <li class="${levelClass}">
                <a href="#" class="category-link ${isActive ? 'active' : ''} ${hasChildren && isExpanded ? 'expanded' : ''}"
                   data-category-id="${category.id}"
                   data-has-children="${hasChildren}"
                   data-level="${level}"
                   ${indentStyle}
                   role="button"
                   tabindex="0"
                   aria-expanded="${hasChildren ? isExpanded : 'false'}"
                   aria-label="${category.name}${hasChildren ? ' 分类' : ''}${siteCount > 0 ? `, ${siteCount} 个网站` : ''}">
                    <span class="category-icon" aria-hidden="true">${category.icon || '📁'}</span>
                    <span class="category-name">${category.name}</span>
                    ${countDisplay}
                    ${hasChildren ? '<i class="fas fa-chevron-right category-arrow" aria-hidden="true" title="展开/折叠子分类"></i>' : ''}
                </a>
        `;
        
        // 渲染子分类
        if (hasChildren) {
            html += `
                <ul class="subcategory-list ${isExpanded ? 'expanded' : ''}"
                    style="display: ${isExpanded ? 'block' : 'none'};"
                    aria-expanded="${isExpanded}"
                    role="list">
                    ${category.children.map(child => this.renderCategoryItem(child, level + 1)).join('')}
                </ul>
            `;
        }
        
        html += '</li>';
        
        return html;
    }
    
    /**
     * 绑定分类事件（增强版本）
     */
    bindCategoryEvents() {
        const categoryLinks = this.categoryList.querySelectorAll('.category-link');

        console.log(`绑定分类事件，找到 ${categoryLinks.length} 个分类链接`);

        categoryLinks.forEach((link, index) => {
            // 移除之前可能存在的事件监听器
            link.removeEventListener('click', this.handleCategoryClick);

            // 鼠标点击事件
            link.addEventListener('click', (e) => {
                console.log(`分类链接 ${index} 被点击:`, link.dataset.categoryId);
                this.handleCategoryClick(link, e);
            }, { passive: false });

            // 触摸事件支持（移动端）
            link.addEventListener('touchend', (e) => {
                console.log(`分类链接 ${index} 触摸结束:`, link.dataset.categoryId);
                // 防止触摸后立即触发click事件
                e.preventDefault();
                this.handleCategoryClick(link, e);
            }, { passive: false });

            // 键盘导航支持
            this.addKeyboardSupport(link);

            // 悬停效果增强
            link.addEventListener('mouseenter', () => {
                this.handleCategoryHover(link);
            });

            // 长按支持（移动端）
            this.addLongPressSupport(link);
        });
    }

    /**
     * 处理分类悬停
     * @param {Element} link 分类链接
     */
    handleCategoryHover(link) {
        // 预加载相关内容（如果需要）
        const categoryId = link.dataset.categoryId;
        if (this.navApp && typeof this.navApp.preloadCategory === 'function') {
            this.navApp.preloadCategory(categoryId);
        }
    }

    /**
     * 添加长按支持（移动端友好）
     * @param {Element} link 分类链接
     */
    addLongPressSupport(link) {
        let pressTimer = null;
        
        link.addEventListener('touchstart', (e) => {
            pressTimer = setTimeout(() => {
                // 长按操作：快速展开/折叠所有子分类
                const categoryId = link.dataset.categoryId;
                const hasChildren = link.dataset.hasChildren === 'true';
                
                if (hasChildren) {
                    this.toggleAllSubcategories(categoryId);
                    // 触感反馈（如果支持）
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                }
            }, 500);
        });
        
        link.addEventListener('touchend', () => {
            if (pressTimer) {
                clearTimeout(pressTimer);
                pressTimer = null;
            }
        });
        
        link.addEventListener('touchmove', () => {
            if (pressTimer) {
                clearTimeout(pressTimer);
                pressTimer = null;
            }
        });
    }

    /**
     * 切换所有子分类的展开状态
     * @param {string} categoryId 分类ID
     */
    toggleAllSubcategories(categoryId) {
        // 实现展开/折叠所有子分类的逻辑
        const categoryData = this.findCategoryData(categoryId);
        if (categoryData && categoryData.children) {
            const allExpanded = categoryData.children.every(child => 
                this.expandedCategories.has(child.id)
            );
            
            categoryData.children.forEach(child => {
                if (allExpanded) {
                    this.expandedCategories.delete(child.id);
                    this.updateCategoryExpanded(child.id, false);
                } else {
                    this.expandedCategories.add(child.id);
                    this.updateCategoryExpanded(child.id, true);
                }
            });
            
            this.saveExpandedState();
        }
    }

    /**
     * 查找分类数据
     * @param {string} categoryId 分类ID
     * @returns {Object|null} 分类数据
     */
    findCategoryData(categoryId) {
        // 这个方法需要与主应用配合实现
        if (this.navApp && typeof this.navApp.findCategory === 'function') {
            return this.navApp.findCategory(categoryId);
        }
        return null;
    }
    
    /**
     * 处理分类点击（性能优化版本 - 移除防抖和动画检查）
     * @param {Element} link 点击的分类链接
     * @param {Event} event 点击事件
     */
    handleCategoryClick(link, event) {
        // 确保事件不会被阻止
        event.preventDefault();
        event.stopPropagation();

        const categoryId = link.dataset.categoryId;
        const hasChildren = link.dataset.hasChildren === 'true';

        console.log('分类点击事件:', { categoryId, hasChildren, isMobile: isMobile() });

        // 检查点击的是分类名称还是展开箭头
        const clickTarget = event.target;
        const isArrowClick = clickTarget.classList.contains('category-arrow') ||
                           clickTarget.closest('.category-arrow');

        if (hasChildren && isArrowClick) {
            // 点击箭头时，只展开/折叠子分类
            console.log('点击箭头，切换展开状态');
            this.toggleCategoryExpanded(categoryId);
        } else {
            // 点击分类名称时，切换到该分类显示内容
            console.log('点击分类名称，切换分类');
            this.setActiveCategory(categoryId);

            // 如果有子分类，同时展开显示子分类列表
            if (hasChildren) {
                if (!this.expandedCategories.has(categoryId)) {
                    this.toggleCategoryExpanded(categoryId);
                }
            }

            // 通知主应用切换分类
            if (this.navApp && typeof this.navApp.switchCategory === 'function') {
                this.navApp.switchCategory(categoryId);

                // 如果当前是显示所有分类模式，滚动到对应分类
                if (this.navApp.isShowingAllCategories) {
                    this.scrollToCategorySection(categoryId);
                }
            }

            // 移动端切换分类后关闭侧边栏
            if (isMobile()) {
                console.log('移动端，关闭侧边栏');
                this.closeMobileSidebar();
            }
        }
    }
    
    /**
     * 切换分类展开状态（性能优化版本 - 即时切换）
     * @param {string} categoryId 分类ID
     */
    toggleCategoryExpanded(categoryId) {
        const isExpanded = this.expandedCategories.has(categoryId);

        if (isExpanded) {
            this.expandedCategories.delete(categoryId);
        } else {
            this.expandedCategories.add(categoryId);
        }

        this.updateCategoryExpanded(categoryId, !isExpanded);
        this.saveExpandedState();
    }
    
    /**
     * 更新分类展开状态UI（性能优化版本 - 即时切换，无动画）
     * @param {string} categoryId 分类ID
     * @param {boolean} expanded 是否展开
     */
    updateCategoryExpanded(categoryId, expanded) {
        const categoryLink = this.categoryList.querySelector(`[data-category-id="${categoryId}"]`);
        if (!categoryLink) return;

        const subcategoryList = categoryLink.parentElement.querySelector('.subcategory-list');

        // 即时更新展开状态
        if (expanded) {
            categoryLink.classList.add('expanded');
            if (subcategoryList) {
                subcategoryList.style.display = 'block';
                subcategoryList.classList.add('expanded');
            }
        } else {
            categoryLink.classList.remove('expanded');
            if (subcategoryList) {
                subcategoryList.style.display = 'none';
                subcategoryList.classList.remove('expanded');
            }
        }
    }

    /**
     * 添加交互反馈动画（已禁用以提升性能）
     * @param {Element} element 目标元素
     */
    addInteractionFeedback(element) {
        // 移除动画以提升性能
        return;
    }

    /**
     * 滚动到活动分类（优化版本）
     * @param {Element} activeLink 活动分类链接
     */
    scrollToActiveCategory(activeLink) {
        if (activeLink && this.sidebar) {
            // 检查元素是否在可视区域内
            const sidebarRect = this.sidebar.getBoundingClientRect();
            const linkRect = activeLink.getBoundingClientRect();
            
            const isVisible = (
                linkRect.top >= sidebarRect.top &&
                linkRect.bottom <= sidebarRect.bottom
            );
            
            if (!isVisible) {
                activeLink.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            }
        }
    }

    /**
     * 预加载分类动画（已禁用以提升性能）
     */
    preloadCategoryAnimations() {
        // 移除动画预加载以提升性能
        return;
    }

    /**
     * 移动端侧边栏打开（性能优化版本）
     */
    openMobileSidebar() {
        if (!isMobile()) return;

        this.sidebar?.classList.add('active');
        this.sidebarOverlay?.classList.add('active');
        this.isMobileSidebarOpen = true;

        // 阻止背景滚动
        document.body.style.overflow = 'hidden';

        console.log('Mobile sidebar opened (performance optimized)');
    }

    /**
     * 移动端侧边栏关闭（性能优化版本）
     */
    closeMobileSidebar() {
        this.sidebar?.classList.remove('active');
        this.sidebarOverlay?.classList.remove('active');
        this.isMobileSidebarOpen = false;

        // 恢复背景滚动
        document.body.style.overflow = '';

        console.log('Mobile sidebar closed (performance optimized)');
    }
    
    /**
     * 保存展开状态
     */
    saveExpandedState() {
        const expandedArray = Array.from(this.expandedCategories);
        localStorage.setItem('navsphere-expanded-categories', JSON.stringify(expandedArray));
        console.log('Expanded state saved:', expandedArray);
    }
    
    /**
     * 同步加载展开状态（在构造函数中调用）
     */
    loadExpandedStateSync() {
        try {
            const saved = localStorage.getItem('navsphere-expanded-categories');
            if (saved) {
                const expandedArray = JSON.parse(saved);
                this.expandedCategories = new Set(expandedArray);
                console.log('Expanded state loaded (sync):', expandedArray);
            }

            // 加载当前活动分类
            const savedCategory = localStorage.getItem('navsphere-current-category');
            if (savedCategory) {
                // 验证保存的分类是否仍然有效
                if (savedCategory === 'recommend' || savedCategory === 'core-operations') {
                    // 如果是旧的默认分类，使用智能默认分类选择
                    console.log('检测到旧的默认分类，使用智能默认分类选择');
                    this.currentCategory = this.getSmartDefaultCategory();
                    this.saveCurrentCategory(); // 保存更新后的分类
                } else {
                    this.currentCategory = savedCategory;
                    console.log('Current category loaded (sync):', savedCategory);
                }
            } else {
                // 首次访问，使用智能默认分类选择
                this.currentCategory = this.getSmartDefaultCategory();
                console.log('首次访问，使用智能默认分类:', this.currentCategory);
            }
        } catch (error) {
            console.error('Failed to load expanded state (sync):', error);
            this.expandedCategories = new Set();
            // 重置为智能默认分类
            this.currentCategory = this.getSmartDefaultCategory();
        }
    }

    /**
     * 加载展开状态（DOM加载完成后调用，用于UI更新）
     */
    loadExpandedState() {
        // 状态已经在构造函数中加载，这里只需要确保UI同步
        console.log('Expanded state already loaded, current category:', this.currentCategory);
    }
    
    /**
     * 保存当前分类状态
     */
    saveCurrentCategory() {
        localStorage.setItem('navsphere-current-category', this.currentCategory);
    }
    
    /**
     * 恢复保存的展开状态（无动画版本）
     */
    restoreExpandedState() {
        const categoryLinks = this.categoryList.querySelectorAll('.category-link');
        categoryLinks.forEach(link => {
            const categoryId = link.dataset.categoryId;
            const hasChildren = link.dataset.hasChildren === 'true';
            const isExpanded = this.expandedCategories.has(categoryId);

            if (hasChildren) {
                // 直接设置状态，不触发动画
                this.setExpandedStateDirectly(categoryId, isExpanded);
            }
        });
    }

    /**
     * 直接设置展开状态（无动画，用于初始化）
     * @param {string} categoryId 分类ID
     * @param {boolean} expanded 是否展开
     */
    setExpandedStateDirectly(categoryId, expanded) {
        const categoryLink = this.categoryList.querySelector(`[data-category-id="${categoryId}"]`);
        if (!categoryLink) return;

        const subcategoryList = categoryLink.parentElement.querySelector('.subcategory-list');

        if (expanded) {
            categoryLink.classList.add('expanded');
            if (subcategoryList) {
                subcategoryList.style.display = 'block';
                subcategoryList.classList.add('expanded');
            }
        } else {
            categoryLink.classList.remove('expanded');
            if (subcategoryList) {
                subcategoryList.style.display = 'none';
                subcategoryList.classList.remove('expanded');
            }
        }
    }
    
    /**
     * 获取当前分类
     * @returns {string} 当前分类ID
     */
    getCurrentCategory() {
        return this.currentCategory;
    }
    
    /**
     * 重置侧边栏状态
     */
    reset() {
        this.currentCategory = 'all-categories'; // 重置为所有分类模式
        this.expandedCategories.clear();
        this.closeMobileSidebar();
        localStorage.removeItem('navsphere-expanded-categories');
        localStorage.removeItem('navsphere-current-category');
    }
    
    /**
     * 检查移动端侧边栏是否打开
     * @returns {boolean} 是否打开
     */
    isMobileOpen() {
        return this.isMobileSidebarOpen;
    }

    /**
     * 获取分类中的网站数量
     * @param {string} categoryId 分类ID
     * @returns {number} 网站数量
     */
    getCategorySiteCount(categoryId) {
        if (!this.navApp || !this.navApp.getSitesByCategory) {
            return 0;
        }

        const sites = this.navApp.getSitesByCategory(categoryId);
        return sites ? sites.length : 0;
    }

    /**
     * 获取智能默认分类
     * 如果用户有访问历史，默认显示最近访问分类；否则显示全部分类
     * @returns {string} 默认分类ID
     */
    getSmartDefaultCategory() {
        // 检查是否有访问历史
        if (this.navApp && this.navApp.visitManager) {
            const visitStats = this.navApp.visitManager.getStats();
            if (visitStats.uniqueSites > 0) {
                console.log('检测到访问历史，默认显示最近访问分类');
                return 'frequent';
            }
        }

        // 首次访问或没有访问历史，显示全部分类
        console.log('首次访问或无访问历史，默认显示全部分类');
        return 'all-categories';
    }

    /**
     * 获取最近访问网站的数量
     * @returns {number} 最近访问网站数量
     */
    getFrequentSitesCount() {
        if (this.navApp && this.navApp.visitManager) {
            const frequentSites = this.navApp.getFrequentSites();
            return frequentSites.length;
        }
        return 0;
    }

    /**
     * 更新最近访问分类的计数显示
     */
    updateFrequentCount() {
        const frequentLink = this.categoryList?.querySelector('[data-category-id="frequent"]');
        if (frequentLink) {
            const countElement = frequentLink.querySelector('.category-count');
            const frequentCount = this.getFrequentSitesCount();

            if (countElement) {
                countElement.textContent = frequentCount;
            }

            // 更新title提示
            const title = frequentCount === 0 ?
                '点击网站卡片后，它们将出现在这里' :
                `${frequentCount} 个最近访问网站`;
            frequentLink.setAttribute('title', title);
            frequentLink.setAttribute('aria-label', `最近访问 - ${title}`);
        }
    }

    /**
     * 获取所有网站的总数量
     * @returns {number} 总网站数量
     */
    getTotalSitesCount() {
        if (this.navApp && this.navApp.allSites && this.navApp.allSites.length > 0) {
            return this.navApp.allSites.length;
        }

        // 如果主应用还没有初始化，尝试从数据中计算
        if (this.navApp && this.navApp.data && this.navApp.data.categories) {
            let totalCount = 0;
            const countSitesInCategory = (category) => {
                if (category.sites) {
                    totalCount += category.sites.length;
                }
                if (category.children) {
                    category.children.forEach(countSitesInCategory);
                }
            };
            this.navApp.data.categories.forEach(countSitesInCategory);
            return totalCount;
        }

        return 0;
    }

    /**
     * 滚动到指定分类区域
     * @param {string} categoryId 分类ID
     */
    scrollToCategorySection(categoryId) {
        // 特殊处理全部分类
        if (categoryId === 'all-categories') {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            return;
        }

        const categorySection = document.querySelector(`[data-category-id="${categoryId}"].category-section`);
        if (categorySection) {
            const navbarHeight = 64;
            const offset = 20;
            const targetPosition = categorySection.offsetTop - navbarHeight - offset;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });

            console.log(`滚动到分类区域: ${categoryId}`);
        }
    }

    /**
     * 添加键盘导航支持
     * @param {Element} categoryLink 分类链接元素
     */
    addKeyboardSupport(categoryLink) {
        categoryLink.addEventListener('keydown', (e) => {
            const categoryId = categoryLink.dataset.categoryId;
            const hasChildren = categoryLink.dataset.hasChildren === 'true';
            
            switch (e.key) {
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    this.handleCategoryClick(categoryLink, e);
                    break;
                    
                case 'ArrowRight':
                    if (hasChildren && !this.expandedCategories.has(categoryId)) {
                        e.preventDefault();
                        this.toggleCategoryExpanded(categoryId);
                    }
                    break;
                    
                case 'ArrowLeft':
                    if (hasChildren && this.expandedCategories.has(categoryId)) {
                        e.preventDefault();
                        this.toggleCategoryExpanded(categoryId);
                    }
                    break;
                    
                case 'Home':
                    e.preventDefault();
                    this.focusFirstCategory();
                    break;
                    
                case 'End':
                    e.preventDefault();
                    this.focusLastCategory();
                    break;
            }
        });
    }

    /**
     * 聚焦到第一个分类
     */
    focusFirstCategory() {
        const firstCategory = this.categoryList.querySelector('.category-link');
        if (firstCategory) {
            firstCategory.focus();
        }
    }

    /**
     * 聚焦到最后一个分类
     */
    focusLastCategory() {
        const allCategories = this.categoryList.querySelectorAll('.category-link');
        const lastCategory = allCategories[allCategories.length - 1];
        if (lastCategory) {
            lastCategory.focus();
        }
    }
}

// 导出侧边栏管理器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SidebarManager;
} 