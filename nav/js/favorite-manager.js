/**
 * 收藏管理器
 * 负责管理用户的网站收藏功能
 * 支持数据持久化、添加/删除收藏等功能
 */
class FavoriteManager {
    constructor() {
        this.storageKey = 'navsphere-favorites';
        this.maxFavorites = 100; // 最多收藏100个网站
        this.favorites = [];
        this.config = null; // 应用配置

        // 初始化收藏数据
        this.init();
    }

    /**
     * 初始化收藏管理器
     */
    async init() {
        try {
            // 加载配置
            await this.loadConfig();

            this.favorites = this.loadFavorites();
            console.log(`收藏管理器初始化完成，已加载 ${this.favorites.length} 个收藏`);
        } catch (error) {
            console.error('收藏管理器初始化失败:', error);
            this.favorites = [];
        }
    }

    /**
     * 加载应用配置
     */
    async loadConfig() {
        try {
            const response = await fetch('nav/data/appconfig.json');
            if (response.ok) {
                this.config = await response.json();

                // 从配置中更新存储数量
                if (this.config.favoriteCategory) {
                    if (this.config.favoriteCategory.storageCount) {
                        this.maxFavorites = this.config.favoriteCategory.storageCount;
                    }
                }

                console.log(`收藏配置已加载: 最多存储${this.maxFavorites}个收藏`);
            } else {
                console.warn('无法加载配置文件，使用默认收藏配置');
            }
        } catch (error) {
            console.warn('加载配置文件失败，使用默认收藏配置:', error);
        }
    }



    /**
     * 从本地存储加载收藏数据
     * @returns {Array} 收藏列表
     */
    loadFavorites() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const favorites = JSON.parse(stored);
                
                // 验证数据格式
                if (Array.isArray(favorites)) {
                    return favorites.filter(item => 
                        item && 
                        typeof item.siteId === 'string' && 
                        typeof item.siteName === 'string'
                    );
                }
            }
        } catch (error) {
            console.error('加载收藏数据失败:', error);
        }

        console.log('初始化空的收藏列表');
        return [];
    }

    /**
     * 保存收藏数据到本地存储
     */
    saveFavorites() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.favorites));
            console.log(`收藏数据已保存: ${this.favorites.length} 个收藏`);
        } catch (error) {
            console.error('保存收藏数据失败:', error);
        }
    }

    /**
     * 添加网站到收藏
     * @param {string} siteId 网站ID
     * @param {string} siteName 网站名称
     * @param {Object} siteData 网站完整数据（可选）
     * @returns {boolean} 是否添加成功
     */
    addFavorite(siteId, siteName, siteData = null) {
        if (!siteId || !siteName) {
            console.warn('添加收藏失败: siteId 或 siteName 为空');
            return false;
        }

        // 检查是否已经收藏
        if (this.isFavorite(siteId)) {
            console.log(`网站 ${siteName} 已在收藏列表中`);
            return false;
        }

        // 检查收藏数量限制
        if (this.favorites.length >= this.maxFavorites) {
            console.warn(`收藏数量已达上限 (${this.maxFavorites})`);
            return false;
        }

        const now = Date.now();
        const favoriteItem = {
            siteId,
            siteName,
            addedAt: now,
            ...(siteData && { siteData }) // 如果提供了完整数据，保存它
        };

        this.favorites.unshift(favoriteItem); // 添加到列表开头
        this.saveFavorites();

        console.log(`已添加收藏: ${siteName}`);
        return true;
    }

    /**
     * 从收藏中移除网站
     * @param {string} siteId 网站ID
     * @returns {boolean} 是否移除成功
     */
    removeFavorite(siteId) {
        if (!siteId) {
            console.warn('移除收藏失败: siteId 为空');
            return false;
        }

        const initialLength = this.favorites.length;
        this.favorites = this.favorites.filter(item => item.siteId !== siteId);

        if (this.favorites.length < initialLength) {
            this.saveFavorites();
            console.log(`已移除收藏: ${siteId}`);
            return true;
        }

        console.log(`收藏列表中未找到: ${siteId}`);
        return false;
    }

    /**
     * 检查网站是否已收藏
     * @param {string} siteId 网站ID
     * @returns {boolean} 是否已收藏
     */
    isFavorite(siteId) {
        return this.favorites.some(item => item.siteId === siteId);
    }

    /**
     * 获取收藏列表
     * @param {number} limit 返回数量限制，默认返回所有
     * @returns {Array} 收藏网站信息列表
     */
    getFavorites(limit = null) {
        const result = limit ? this.favorites.slice(0, limit) : [...this.favorites];
        
        return result.map(item => ({
            siteId: item.siteId,
            siteName: item.siteName,
            addedAt: item.addedAt,
            siteData: item.siteData || null
        }));
    }

    /**
     * 获取收藏网站ID列表
     * @param {number} limit 返回数量限制，默认返回所有
     * @returns {Array} 网站ID数组
     */
    getFavoriteSiteIds(limit = null) {
        const result = limit ? this.favorites.slice(0, limit) : this.favorites;
        return result.map(item => item.siteId);
    }

    /**
     * 获取收藏统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            totalFavorites: this.favorites.length,
            maxFavorites: this.maxFavorites,
            oldestFavorite: this.favorites.length > 0 ? 
                Math.min(...this.favorites.map(item => item.addedAt)) : null,
            newestFavorite: this.favorites.length > 0 ? 
                Math.max(...this.favorites.map(item => item.addedAt)) : null
        };
    }

    /**
     * 切换收藏状态
     * @param {string} siteId 网站ID
     * @param {string} siteName 网站名称
     * @param {Object} siteData 网站完整数据（可选）
     * @returns {boolean} 切换后的收藏状态
     */
    toggleFavorite(siteId, siteName, siteData = null) {
        if (this.isFavorite(siteId)) {
            this.removeFavorite(siteId);
            return false;
        } else {
            this.addFavorite(siteId, siteName, siteData);
            return true;
        }
    }

    /**
     * 清空所有收藏
     */
    clearFavorites() {
        this.favorites = [];
        this.saveFavorites();
        console.log('已清空所有收藏');
    }

    /**
     * 导出收藏数据
     * @returns {string} JSON格式的收藏数据
     */
    exportFavorites() {
        return JSON.stringify(this.favorites, null, 2);
    }

    /**
     * 导入收藏数据
     * @param {string} jsonData JSON格式的收藏数据
     * @returns {boolean} 是否导入成功
     */
    importFavorites(jsonData) {
        try {
            const importedFavorites = JSON.parse(jsonData);
            
            if (Array.isArray(importedFavorites)) {
                // 验证数据格式
                const validFavorites = importedFavorites.filter(item => 
                    item && 
                    typeof item.siteId === 'string' && 
                    typeof item.siteName === 'string'
                );

                this.favorites = validFavorites;
                this.saveFavorites();
                
                console.log(`已导入 ${validFavorites.length} 个收藏`);
                return true;
            }
        } catch (error) {
            console.error('导入收藏数据失败:', error);
        }
        
        return false;
    }

    /**
     * 获取收藏网站的详细信息（需要配合主应用的网站数据）
     * @param {Array} allSites 所有网站数据
     * @param {number} limit 返回数量限制
     * @returns {Array} 收藏网站的详细信息
     */
    getFavoriteDetails(allSites, limit = null) {
        if (!allSites || !Array.isArray(allSites)) {
            return [];
        }

        const favoriteIds = this.getFavoriteSiteIds(limit);
        const favoriteDetails = [];

        favoriteIds.forEach(siteId => {
            const site = allSites.find(s => s.id === siteId);
            if (site) {
                // 添加收藏信息到网站对象
                const favoriteInfo = this.favorites.find(f => f.siteId === siteId);
                const siteWithFavoriteInfo = {
                    ...site,
                    favoriteInfo: {
                        addedAt: favoriteInfo.addedAt,
                        isFavorite: true
                    }
                };
                favoriteDetails.push(siteWithFavoriteInfo);
            }
        });

        console.log(`获取收藏网站详情: ${favoriteDetails.length} 个`);
        return favoriteDetails;
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FavoriteManager;
}
